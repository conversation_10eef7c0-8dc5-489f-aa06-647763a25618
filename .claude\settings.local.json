{"permissions": {"allow": ["Bash(npm uninstall:*)", "Bash(npm install:*)", "<PERSON><PERSON>(Compress-Archive -Path ./* -DestinationPath ./crawler-lambda.zip -Force)", "<PERSON><PERSON>(powershell:*)", "Bash(aws lambda update-function-code:*)", "Bash(ls:*)", "Bash(zip:*)", "Bash(tar:*)", "Bash(aws lambda update-function-configuration:*)", "Bash(aws lambda invoke:*)", "<PERSON><PERSON>(cat:*)", "Bash(rm:*)", "Bash(npm ls:*)", "Bash(find:*)", "<PERSON><PERSON>(python:*)", "Bash(aws s3 cp:*)", "Bash(aws lambda delete-function:*)", "Bash(aws:*)", "Bash(cp:*)", "Bash(npm run dev:*)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(taskkill:*)", "Bash(grep:*)", "Bash(npm run build:*)", "<PERSON><PERSON>(del backend-startup.sh)", "Bash(git add:*)", "Bash(git commit:*)", "WebFetch(domain:stackoverflow.com)", "WebFetch(domain:www.browserless.io)", "Bash(npm start)", "Bash(psql:*)", "<PERSON><PERSON>(timeout:*)", "Bash(node:*)"], "deny": []}}