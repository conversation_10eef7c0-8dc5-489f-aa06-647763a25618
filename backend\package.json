{"name": "mis2-backend", "version": "1.0.0", "description": "Backend server for MIS2 Cyber Threat Intelligence Crawler", "main": "dist/server.js", "scripts": {"dev": "tsx watch src/server.ts", "build": "tsc", "start": "node dist/server.js", "clean": "<PERSON><PERSON><PERSON> dist", "postinstall": "npm run build && npm run install-browsers", "install-browsers": "npx playwright install chromium || echo 'Playwright browser installation failed, using bundled browser'", "install-browsers-azure": "npx playwright install --with-deps chromium || echo 'Playwright browser installation failed, using bundled browser'", "test-browser": "node test-browser.js"}, "dependencies": {"@sparticuz/chromium": "^131.0.0", "axios": "^1.6.7", "cors": "^2.8.5", "crawlee": "^3.13.9", "dotenv": "^16.4.5", "express": "^4.18.2", "mime-types": "^2.1.35", "openai": "^4.68.4", "pg": "^8.11.3", "playwright": "^1.53.2", "playwright-core": "^1.53.2", "socket.io": "^4.8.1", "uuid": "^9.0.1"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/mime-types": "^2.1.4", "@types/node": "^20.10.4", "@types/pg": "^8.10.9", "@types/uuid": "^9.0.7", "rimraf": "^5.0.5", "tsx": "^4.6.2", "typescript": "^5.3.3"}, "engines": {"node": ">=18.0.0"}}