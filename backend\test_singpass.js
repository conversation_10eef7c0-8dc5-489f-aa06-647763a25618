const { Pool } = require('pg');
const { AzureOpenAI } = require('openai');

const pool = new Pool({
  host: 'database-1-instance-1.c96kcq8k0crv.ap-southeast-1.rds.amazonaws.com',
  port: 5432,
  user: 'postgres',
  password: 'postgres',
  database: 'postgres'
});

const embeddingsClient = new AzureOpenAI({
  endpoint: 'https://zhupocai01.openai.azure.com/',
  apiKey: '********************************',
  apiVersion: '2023-05-15',
});

async function generateEmbedding(text) {
  try {
    const truncatedText = text.slice(0, 8000);
    const response = await embeddingsClient.embeddings.create({
      model: 'text-embedding-3-small',
      input: truncatedText,
    });
    return response.data[0]?.embedding || null;
  } catch (error) {
    console.error('Failed to generate embedding:', error);
    return null;
  }
}

async function testSingpassSearch() {
  try {
    // First find the Singpass article
    const singpassArticle = await pool.query(`
      SELECT title, main_text 
      FROM articles 
      WHERE title ILIKE '%singpass%' AND title ILIKE '%credential%'
      LIMIT 1
    `);
    
    if (singpassArticle.rows.length === 0) {
      console.log('❌ Singpass credential article not found');
      return;
    }
    
    console.log('Found Singpass article:', singpassArticle.rows[0].title);
    
    // Test different query variations
    const queries = [
      'Singpass credential sharing',
      'credential sharing',
      'Singpass',
      'pass credential sharing',
      'digital credential sharing',
      'login credential sharing'
    ];
    
    for (const query of queries) {
      console.log(`\n--- Testing query: "${query}" ---`);
      
      const queryEmbedding = await generateEmbedding(query);
      if (!queryEmbedding) continue;
      
      const searchQuery = `
        SELECT 
          title,
          (1 - (vector <=> $1::vector)) as similarity
        FROM articles 
        WHERE vector IS NOT NULL
        AND (title ILIKE '%singpass%' OR title ILIKE '%credential%')
        ORDER BY vector <=> $1::vector
        LIMIT 5;
      `;
      
      const vectorParam = `[${queryEmbedding.join(',')}]`;
      const result = await pool.query(searchQuery, [vectorParam]);
      
      console.log(`Found ${result.rows.length} credential-related articles:`);
      result.rows.forEach((row, index) => {
        console.log(`${index + 1}. ${row.title} (similarity: ${row.similarity.toFixed(3)})`);
      });
    }
    
    // Check what the current threshold is in the database service
    console.log('\n--- Checking current search implementation threshold ---');
    console.log('The searchSimilarArticles function uses threshold 0.7 by default');
    console.log('The searchSimilarArticlesForChat function uses threshold 0.1 by default');
    console.log('Maximum similarity found was around 0.31, which is below 0.7 threshold');
    
  } catch (error) {
    console.error('Error testing Singpass search:', error);
  } finally {
    await pool.end();
  }
}

testSingpassSearch();