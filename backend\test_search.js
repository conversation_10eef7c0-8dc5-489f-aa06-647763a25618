const { Pool } = require('pg');

const pool = new Pool({
  host: 'database-1-instance-1.c96kcq8k0crv.ap-southeast-1.rds.amazonaws.com',
  port: 5432,
  user: 'postgres',
  password: 'postgres',
  database: 'postgres'
});

async function testSimilaritySearch() {
  try {
    console.log('Testing similarity search for "credential sharing"...');
    
    // Test direct SQL similarity search
    const testQuery = `
      SELECT 
        url,
        title,
        similarity(title, 'credential sharing') as title_similarity,
        similarity(main_text, 'credential sharing') as text_similarity,
        vector IS NOT NULL as has_vector
      FROM articles
      WHERE similarity(title, 'credential sharing') > 0.1 
         OR similarity(main_text, 'credential sharing') > 0.05
      ORDER BY GREATEST(
        similarity(title, 'credential sharing'),
        similarity(main_text, 'credential sharing')
      ) DESC
      LIMIT 5
    `;
    
    const result = await pool.query(testQuery);
    
    console.log(`Found ${result.rows.length} articles with text similarity:`)
    result.rows.forEach(row => {
      console.log(`Title: ${row.title}`);
      console.log(`Title Similarity: ${row.title_similarity}`);
      console.log(`Text Similarity: ${row.text_similarity}`);
      console.log(`Has Vector: ${row.has_vector}`);
      console.log('---');
    });
    
    // Test simple ILIKE search
    const ilikeResult = await pool.query(`
      SELECT url, title, vector IS NOT NULL as has_vector
      FROM articles 
      WHERE title ILIKE '%credential%' OR main_text ILIKE '%credential%'
      LIMIT 5
    `);
    
    console.log(`\nILIKE search found ${ilikeResult.rows.length} articles:`);
    ilikeResult.rows.forEach(row => {
      console.log(`Title: ${row.title}, Has Vector: ${row.has_vector}`);
    });
    
  } catch (error) {
    console.error('Error testing similarity search:', error);
  } finally {
    await pool.end();
  }
}

testSimilaritySearch();