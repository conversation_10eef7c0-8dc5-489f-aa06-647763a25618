import React from 'react';
import { motion } from 'framer-motion';

interface GlowEffectProps {
  children: React.ReactNode;
  color?: 'blue' | 'purple' | 'green' | 'red' | 'orange';
  intensity?: 'low' | 'medium' | 'high';
  className?: string;
  animate?: boolean;
}

export const GlowEffect: React.FC<GlowEffectProps> = ({
  children,
  color = 'blue',
  intensity = 'medium',
  className = '',
  animate = true
}) => {
  const colorMap = {
    blue: {
      low: 'drop-shadow-[0_0_10px_rgba(59,130,246,0.3)]',
      medium: 'drop-shadow-[0_0_20px_rgba(59,130,246,0.4)]',
      high: 'drop-shadow-[0_0_30px_rgba(59,130,246,0.6)]',
      glowClass: 'shadow-[0_0_20px_rgba(59,130,246,0.3)]',
      hoverClass: 'hover:shadow-[0_0_40px_rgba(59,130,246,0.5)]'
    },
    purple: {
      low: 'drop-shadow-[0_0_10px_rgba(147,51,234,0.3)]',
      medium: 'drop-shadow-[0_0_20px_rgba(147,51,234,0.4)]',
      high: 'drop-shadow-[0_0_30px_rgba(147,51,234,0.6)]',
      glowClass: 'shadow-[0_0_20px_rgba(147,51,234,0.3)]',
      hoverClass: 'hover:shadow-[0_0_40px_rgba(147,51,234,0.5)]'
    },
    green: {
      low: 'drop-shadow-[0_0_10px_rgba(16,185,129,0.3)]',
      medium: 'drop-shadow-[0_0_20px_rgba(16,185,129,0.4)]',
      high: 'drop-shadow-[0_0_30px_rgba(16,185,129,0.6)]',
      glowClass: 'shadow-[0_0_20px_rgba(16,185,129,0.3)]',
      hoverClass: 'hover:shadow-[0_0_40px_rgba(16,185,129,0.5)]'
    },
    red: {
      low: 'drop-shadow-[0_0_10px_rgba(239,68,68,0.3)]',
      medium: 'drop-shadow-[0_0_20px_rgba(239,68,68,0.4)]',
      high: 'drop-shadow-[0_0_30px_rgba(239,68,68,0.6)]',
      glowClass: 'shadow-[0_0_20px_rgba(239,68,68,0.3)]',
      hoverClass: 'hover:shadow-[0_0_40px_rgba(239,68,68,0.5)]'
    },
    orange: {
      low: 'drop-shadow-[0_0_10px_rgba(245,158,11,0.3)]',
      medium: 'drop-shadow-[0_0_20px_rgba(245,158,11,0.4)]',
      high: 'drop-shadow-[0_0_30px_rgba(245,158,11,0.6)]',
      glowClass: 'shadow-[0_0_20px_rgba(245,158,11,0.3)]',
      hoverClass: 'hover:shadow-[0_0_40px_rgba(245,158,11,0.5)]'
    }
  };

  const glowClasses = colorMap[color];
  
  if (!animate) {
    return (
      <div className={`${glowClasses[intensity]} ${glowClasses.glowClass} ${glowClasses.hoverClass} transition-all duration-500 ${className}`}>
        {children}
      </div>
    );
  }

  return (
    <motion.div
      className={`${glowClasses.glowClass} transition-all duration-500 ${className}`}
      animate={{
        filter: [
          `drop-shadow(0 0 ${intensity === 'low' ? '10px' : intensity === 'medium' ? '20px' : '30px'} ${
            color === 'blue' ? 'rgba(59,130,246,0.3)' :
            color === 'purple' ? 'rgba(147,51,234,0.3)' :
            color === 'green' ? 'rgba(16,185,129,0.3)' :
            color === 'red' ? 'rgba(239,68,68,0.3)' :
            'rgba(245,158,11,0.3)'
          })`,
          `drop-shadow(0 0 ${intensity === 'low' ? '15px' : intensity === 'medium' ? '30px' : '45px'} ${
            color === 'blue' ? 'rgba(59,130,246,0.5)' :
            color === 'purple' ? 'rgba(147,51,234,0.5)' :
            color === 'green' ? 'rgba(16,185,129,0.5)' :
            color === 'red' ? 'rgba(239,68,68,0.5)' :
            'rgba(245,158,11,0.5)'
          })`,
          `drop-shadow(0 0 ${intensity === 'low' ? '10px' : intensity === 'medium' ? '20px' : '30px'} ${
            color === 'blue' ? 'rgba(59,130,246,0.3)' :
            color === 'purple' ? 'rgba(147,51,234,0.3)' :
            color === 'green' ? 'rgba(16,185,129,0.3)' :
            color === 'red' ? 'rgba(239,68,68,0.3)' :
            'rgba(245,158,11,0.3)'
          })`
        ]
      }}
      transition={{
        duration: 3,
        repeat: Infinity,
        ease: "easeInOut"
      }}
      whileHover={{
        filter: `drop-shadow(0 0 ${intensity === 'low' ? '20px' : intensity === 'medium' ? '40px' : '60px'} ${
          color === 'blue' ? 'rgba(59,130,246,0.6)' :
          color === 'purple' ? 'rgba(147,51,234,0.6)' :
          color === 'green' ? 'rgba(16,185,129,0.6)' :
          color === 'red' ? 'rgba(239,68,68,0.6)' :
          'rgba(245,158,11,0.6)'
        })`,
        scale: 1.02,
        transition: { duration: 0.3 }
      }}
    >
      {children}
    </motion.div>
  );
};

// Ambient background glow component
export const AmbientGlow: React.FC<{
  color?: string;
  size?: 'sm' | 'md' | 'lg';
  position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right' | 'center';
}> = ({ 
  color = 'blue', 
  size = 'md',
  position = 'top-right'
}) => {
  const sizeMap = {
    sm: 'w-32 h-32',
    md: 'w-64 h-64',
    lg: 'w-96 h-96'
  };

  const positionMap = {
    'top-left': 'top-0 left-0',
    'top-right': 'top-0 right-0',
    'bottom-left': 'bottom-0 left-0',
    'bottom-right': 'bottom-0 right-0',
    'center': 'top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2'
  };

  return (
    <motion.div
      className={`fixed ${positionMap[position]} ${sizeMap[size]} pointer-events-none`}
      style={{
        background: `radial-gradient(circle, ${
          color === 'blue' ? 'rgba(59, 130, 246, 0.15)' :
          color === 'purple' ? 'rgba(147, 51, 234, 0.15)' :
          color === 'green' ? 'rgba(16, 185, 129, 0.15)' :
          'rgba(59, 130, 246, 0.15)'
        } 0%, transparent 70%)`,
        filter: 'blur(40px)',
        zIndex: -1
      }}
      animate={{
        scale: [1, 1.2, 1],
        opacity: [0.3, 0.6, 0.3]
      }}
      transition={{
        duration: 4,
        repeat: Infinity,
        ease: "easeInOut"
      }}
    />
  );
};