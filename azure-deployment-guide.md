# Azure App Service Deployment Guide for MIS2

## Overview
This guide will help you deploy your full-stack MIS2 application to Azure App Service. The application consists of a React frontend and Node.js backend deployed together on a single Azure Web App. The backend serves both the API endpoints and the built React frontend files.

## Pre-Deployment Setup

### 1. Azure App Service Configuration
In your Azure Portal, go to your App Service (`zpdmlaap01-corvus`) and configure the following:

#### Application Settings (Environment Variables)
Add these in **Configuration > Application Settings**:

```
FRONTEND_URL=https://zpdmlaap01-corvus.azurewebsites.net
NODE_ENV=production
PORT=8080

# Database Configuration (update with your actual values)
DB_HOST=your-database-host
DB_PORT=5432
DB_NAME=your-database-name
DB_USER=your-database-user
DB_PASSWORD=your-database-password

# AI Configuration (optional)
OPENAI_API_KEY=your-openai-api-key
AZURE_OPENAI_API_KEY=your-azure-openai-api-key
AZURE_OPENAI_ENDPOINT=your-azure-openai-endpoint
AZURE_OPENAI_DEPLOYMENT_NAME=gpt-4
```

#### General Settings
- **Runtime stack**: Node 20 LTS
- **Platform**: 64 Bit
- **Always On**: On (recommended)
- **ARR affinity**: Off (for better performance with WebSockets)

### 2. Deployment Configuration
The following files have been created/updated for Azure deployment:

- `web.config` - IIS configuration for both static files and Node.js API routes
- `backend-startup.sh` - Build script that builds frontend and backend together
- `.deployment` - Tells Azure to use the full-stack startup script
- Root `package.json` - Updated with full-stack deployment scripts
- `backend/src/server.ts` - Updated to serve React frontend files and handle client-side routing

**How it works**: The deployment builds the React frontend into static files, then configures the Node.js backend to serve both the API routes and the frontend files.

## Deployment Process

### Option 1: GitHub Actions (Recommended)
1. Push your code to GitHub
2. Set up GitHub Actions deployment in Azure Portal
3. Azure will automatically deploy on each push

### Option 2: Manual Deployment
1. Use Azure CLI or deploy from VS Code
2. The `backend-startup.sh` script will handle building only the backend

## Common Issues and Solutions

### Issue 1: MODULE_NOT_FOUND Error
**Problem**: TypeScript files aren't compiled during deployment.
**Solution**: The `backend-startup.sh` script now builds only the backend before starting the server.

### Issue 2: Vite Build Errors During Deployment
**Problem**: Azure tries to build the frontend React app, causing Vite dependency errors.
**Solution**: The deployment now properly installs all dependencies before building, and builds the frontend first, then the backend.

### Issue 2: WebSocket Connection Issues
**Problem**: Socket.io connections fail in production.
**Solution**: 
- Set `FRONTEND_URL` to your Azure App Service URL
- Enabled WebSockets in `web.config`
- Set ARR affinity to Off

### Issue 3: Database Connection Issues
**Problem**: Database connections fail in production.
**Solution**: Ensure all database environment variables are set correctly in Azure App Service configuration.

### Issue 4: Port Configuration
**Problem**: App fails to start due to port issues.
**Solution**: Azure sets PORT=8080 automatically, the server is configured to use this.

## Verification Steps

After deployment, verify:

1. **Health Check**: Visit `https://zpdmlaap01-corvus.azurewebsites.net/api/health`
2. **Database**: Visit `https://zpdmlaap01-corvus.azurewebsites.net/api/database/test`
3. **Frontend**: Visit `https://zpdmlaap01-corvus.azurewebsites.net`

## Troubleshooting

### Check Logs
1. Go to Azure Portal > App Service > Log stream
2. Or use Azure CLI: `az webapp log tail --name zpdmlaap01-corvus --resource-group your-resource-group`

### Common Log Messages
- "🚀 Starting MIS2 Full-Stack deployment..." - ✅ Deployment script started
- "⚛️ Building React frontend..." - ✅ Frontend compilation in progress
- "🔨 Building backend TypeScript..." - ✅ Backend compilation in progress
- "MIS2 Crawler Backend Server running on port 8080" - ✅ Server started successfully
- "WebSocket server ready for connections" - ✅ WebSocket configured
- "MODULE_NOT_FOUND" - ❌ Build process failed, check backend-startup.sh execution

## File Structure After Deployment
```
/
├── dist/              # Built React frontend (created during deployment)
│   ├── index.html     # React app entry point
│   ├── assets/        # CSS, JS, and other assets
│   └── ...
├── backend/
│   ├── dist/          # Compiled TypeScript (created during deployment)
│   │   └── server.js  # Main entry point that serves API + frontend
│   ├── src/           # Source TypeScript files
│   └── package.json   # Backend dependencies
├── src/               # Frontend React source
├── web.config         # IIS configuration for full-stack app
├── backend-startup.sh # Full-stack build script
├── .deployment        # Azure deployment config
└── package.json       # Root package.json with deployment scripts
```

## Next Steps
1. **Set Environment Variables in Azure Portal**:
   - Go to your Azure App Service > Configuration > Application Settings
   - Add required variables (see environment configuration above)

2. **Update Startup Command**:
   - Go to Configuration > General Settings
   - Set **Startup Command** to: `bash backend-startup.sh`

3. **Deploy Your Code**:
   - Push to GitHub or deploy directly
   - The `backend-startup.sh` script will build both frontend and backend

4. **Verify Deployment**:
   - Check frontend: `https://zpdmlaap01-corvus.azurewebsites.net`
   - Check API health: `https://zpdmlaap01-corvus.azurewebsites.net/api/health`
   - Monitor logs in Azure Portal > Log stream 