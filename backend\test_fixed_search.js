const { Pool } = require('pg');
const { AzureOpenAI } = require('openai');

const pool = new Pool({
  host: 'database-1-instance-1.c96kcq8k0crv.ap-southeast-1.rds.amazonaws.com',
  port: 5432,
  user: 'postgres',
  password: 'postgres',
  database: 'postgres'
});

const embeddingsClient = new AzureOpenAI({
  endpoint: 'https://zhupocai01.openai.azure.com/',
  apiKey: '********************************',
  apiVersion: '2023-05-15',
});

async function generateEmbedding(text) {
  try {
    const truncatedText = text.slice(0, 8000);
    const response = await embeddingsClient.embeddings.create({
      model: 'text-embedding-3-small',
      input: truncatedText,
    });
    return response.data[0]?.embedding || null;
  } catch (error) {
    console.error('Failed to generate embedding:', error);
    return null;
  }
}

async function testFixedSearch() {
  try {
    const queryText = "pass Credential Sharing";
    console.log(`🔍 Testing fixed search for: "${queryText}"`);
    
    const queryEmbedding = await generateEmbedding(queryText);
    if (!queryEmbedding) {
      console.log('❌ Failed to generate embedding');
      return;
    }
    
    console.log(`✅ Generated embedding with ${queryEmbedding.length} dimensions`);
    
    // Test the EXACT query from the fixed searchSimilarArticlesForChat function
    const searchQuery = `
      SELECT 
        url, title, published_date, source, main_text, threat_actor, victim, 
        incident_type, vulnerabilities, tags,
        (vector <=> $1::vector) as distance,
        (1 - (vector <=> $1::vector)) as similarity
      FROM public.articles 
      WHERE vector IS NOT NULL
      ORDER BY vector <=> $1::vector
      LIMIT $2;
    `;
    
    const vectorParam = `[${queryEmbedding.join(',')}]`;
    const result = await pool.query(searchQuery, [vectorParam, 3]);
    
    console.log(`📊 Query executed successfully: ${result.rows.length} articles returned`);
    
    // Apply threshold filtering like the function does
    const threshold = 0.3;
    const filtered = result.rows.filter(row => row.similarity >= threshold);
    
    console.log(`🎯 Articles above threshold ${threshold}: ${filtered.length}`);
    
    result.rows.forEach((row, index) => {
      const aboveThreshold = row.similarity >= threshold ? '✅' : '❌';
      console.log(`${index + 1}. ${aboveThreshold} ${row.title} (similarity: ${row.similarity.toFixed(4)})`);
    });
    
    if (filtered.length > 0) {
      console.log('\n📚 Articles that should be returned as sources:');
      filtered.forEach((row, index) => {
        console.log(`${index + 1}. Title: ${row.title}`);
        console.log(`   URL: ${row.url}`);
        console.log(`   Source: ${row.source}`);
        console.log(`   Similarity: ${row.similarity.toFixed(4)}`);
        console.log('   ---');
      });
    } else {
      console.log('\n❌ No articles above threshold - this is why sources array is empty');
      
      // Test with lower threshold
      console.log('\n🧪 Testing with threshold 0.2:');
      const filtered02 = result.rows.filter(row => row.similarity >= 0.2);
      console.log(`Articles above 0.2: ${filtered02.length}`);
      
      if (filtered02.length > 0) {
        console.log('📝 Suggestion: Lower the threshold to 0.2 to get results');
      }
    }
    
    // Test a better query that might work
    console.log('\n🧪 Testing with "Singpass scam" query:');
    const betterEmbedding = await generateEmbedding("Singpass scam");
    const betterParam = `[${betterEmbedding.join(',')}]`;
    const betterResult = await pool.query(searchQuery, [betterParam, 3]);
    
    const betterFiltered = betterResult.rows.filter(row => row.similarity >= threshold);
    console.log(`"Singpass scam" results above ${threshold}: ${betterFiltered.length}`);
    
    betterResult.rows.forEach((row, index) => {
      const aboveThreshold = row.similarity >= threshold ? '✅' : '❌';
      console.log(`${index + 1}. ${aboveThreshold} ${row.title} (similarity: ${row.similarity.toFixed(4)})`);
    });
    
  } catch (error) {
    console.error('❌ Error testing fixed search:', error);
  } finally {
    await pool.end();
  }
}

testFixedSearch();