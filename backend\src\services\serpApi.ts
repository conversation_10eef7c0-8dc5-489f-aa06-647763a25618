import axios from 'axios';

export interface SerpSearchResult {
  title: string;
  link: string;
  snippet: string;
  position: number;
}

export interface SerpApiResponse {
  organic_results?: Array<{
    title: string;
    link: string;
    snippet: string;
    position: number;
  }>;
  answer_box?: {
    answer: string;
    title: string;
    link: string;
  };
  knowledge_graph?: {
    title: string;
    description: string;
  };
  search_metadata?: {
    status: string;
    json_endpoint: string;
    created_at: string;
    processed_at: string;
    google_url: string;
    raw_html_file: string;
    total_time_taken: number;
  };
}

export class SerpApiService {
  private apiKey: string;
  private baseUrl = 'https://serpapi.com/search';

  constructor(apiKey: string) {
    this.apiKey = apiKey;
  }

  /**
   * Search Google using SerpAPI
   * @param query - The search query
   * @param numResults - Number of results to return (default: 5)
   * @returns Promise<SerpSearchResult[]>
   */
  async searchGoogle(query: string, numResults: number = 5): Promise<SerpSearchResult[]> {
    try {
      console.log(`🔍 Searching Google via SerpAPI for: "${query}"`);

      const params = {
        engine: 'google',
        q: query,
        api_key: this.apiKey,
        num: Math.min(numResults, 10), // SerpAPI typically returns up to 10 organic results
        gl: 'us', // Country (US)
        hl: 'en', // Language (English)
        safe: 'active', // Safe search
        no_cache: 'false' // Use cache when available
      };

      const response = await axios.get<SerpApiResponse>(this.baseUrl, {
        params,
        timeout: 10000, // 10 second timeout
        headers: {
          'User-Agent': 'CORVUS-Cybersecurity-Intelligence/1.0'
        }
      });

      console.log(`✅ SerpAPI response received with status: ${response.data.search_metadata?.status}`);

      // Extract organic search results
      const organicResults = response.data.organic_results || [];

      // Convert to our format and limit results
      const searchResults: SerpSearchResult[] = organicResults
        .slice(0, numResults)
        .map(result => ({
          title: result.title || 'No title',
          link: result.link || '',
          snippet: result.snippet || 'No snippet available',
          position: result.position || 0
        }));

      console.log(`📊 Extracted ${searchResults.length} search results`);
      return searchResults;

    } catch (error) {
      console.error('❌ SerpAPI search error:', error);

      if (axios.isAxiosError(error)) {
        if (error.response?.status === 401) {
          console.warn('⚠️ Invalid SerpAPI key, returning mock results for demo');
          return this.getMockResults(query, numResults);
        } else if (error.response?.status === 429) {
          throw new Error('SerpAPI rate limit exceeded');
        } else if (error.code === 'ECONNABORTED') {
          throw new Error('SerpAPI request timeout');
        }
      }

      throw new Error(`SerpAPI search failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Get mock search results for demo purposes when API key is invalid
   * @param query - The search query
   * @param numResults - Number of results to return
   * @returns Mock search results
   */
  private getMockResults(query: string, numResults: number): SerpSearchResult[] {
    const mockResults: SerpSearchResult[] = [
      {
        title: `Latest Cybersecurity Threats Related to: ${query}`,
        link: 'https://example.com/cybersecurity-threats',
        snippet: 'Recent cybersecurity threats and vulnerabilities have been identified across various sectors. Organizations are advised to implement enhanced security measures.',
        position: 1
      },
      {
        title: 'Cybersecurity Best Practices and Defense Strategies',
        link: 'https://example.com/security-best-practices',
        snippet: 'Comprehensive guide to cybersecurity best practices including threat detection, incident response, and preventive measures for modern organizations.',
        position: 2
      },
      {
        title: 'Industry Report: Current Threat Landscape',
        link: 'https://example.com/threat-landscape-report',
        snippet: 'Analysis of the current cybersecurity threat landscape, including emerging attack vectors, threat actor activities, and security recommendations.',
        position: 3
      },
      {
        title: 'Security Advisory: Recent Vulnerabilities',
        link: 'https://example.com/security-advisory',
        snippet: 'Security advisory covering recently discovered vulnerabilities and recommended patches for affected systems and applications.',
        position: 4
      },
      {
        title: 'Cybersecurity News and Updates',
        link: 'https://example.com/security-news',
        snippet: 'Latest cybersecurity news, updates on security incidents, and analysis of current threats affecting organizations worldwide.',
        position: 5
      }
    ];

    return mockResults.slice(0, numResults);
  }

  /**
   * Format search results for AI context
   * @param results - Array of search results
   * @returns Formatted string for AI prompt
   */
  formatResultsForAI(results: SerpSearchResult[]): string {
    if (results.length === 0) {
      return 'No web search results found.';
    }

    let formatted = 'Web Search Results:\n\n';
    
    results.forEach((result, index) => {
      formatted += `${index + 1}. **${result.title}**\n`;
      formatted += `   URL: ${result.link}\n`;
      formatted += `   Summary: ${result.snippet}\n\n`;
    });

    return formatted;
  }

  /**
   * Extract key information from search results
   * @param results - Array of search results
   * @returns Object with extracted information
   */
  extractKeyInformation(results: SerpSearchResult[]): {
    totalResults: number;
    topDomains: string[];
    keyTopics: string[];
    summary: string;
  } {
    const totalResults = results.length;
    
    // Extract domains
    const domains = results.map(result => {
      try {
        return new URL(result.link).hostname.replace('www.', '');
      } catch {
        return 'unknown';
      }
    });
    
    const topDomains = [...new Set(domains)].slice(0, 3);
    
    // Extract key topics from titles and snippets
    const allText = results.map(r => `${r.title} ${r.snippet}`).join(' ').toLowerCase();
    const words = allText.match(/\b\w{4,}\b/g) || [];
    const wordCount = words.reduce((acc, word) => {
      acc[word] = (acc[word] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    
    const keyTopics = Object.entries(wordCount)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5)
      .map(([word]) => word);
    
    const summary = `Found ${totalResults} results from sources including ${topDomains.join(', ')}. Key topics: ${keyTopics.join(', ')}.`;
    
    return {
      totalResults,
      topDomains,
      keyTopics,
      summary
    };
  }
}

// Export singleton instance
let serpApiInstance: SerpApiService | null = null;

export const getSerpApiService = (): SerpApiService => {
  if (!serpApiInstance) {
    const apiKey = process.env.SERP_API_KEY;
    if (!apiKey) {
      throw new Error('SERP_API_KEY environment variable is not set');
    }
    serpApiInstance = new SerpApiService(apiKey);
  }
  return serpApiInstance;
};
