import React from 'react';
import { Sidebar } from './Sidebar';

interface NavItem {
  id: string;
  icon: React.ReactElement;
  label: string;
  onClick: () => void;
}

interface LayoutProps {
  children: React.ReactNode;
  navItems?: NavItem[];
  activeTabIndex?: number;
  onTabChange?: (index: number) => void;
  currentView?: 'dashboard' | 'crawler' | 'study' | 'chat' | 'scheduler';
  onViewChange?: (view: 'dashboard' | 'crawler' | 'study' | 'chat' | 'scheduler') => void;
}

export const Layout: React.FC<LayoutProps> = ({
  children,
  navItems,
  activeTabIndex = 0,
  onTabChange,
  currentView = 'dashboard',
  onViewChange
}) => {
  return (
    <div className="min-h-screen text-white relative overflow-hidden">
      {/* Plasma Background (updated) */}
      <div className="wrapper pointer-events-none select-none" aria-hidden="true">
        <div className="gradient gradient-1"></div>
        <div className="gradient gradient-2"></div>
        <div className="gradient gradient-3"></div>
      </div>
      {/* Sidebar and Main Content */}
      <Sidebar 
        currentView={currentView}
        onViewChange={onViewChange}
      />
      <main className="ml-64 flex flex-col min-h-screen overflow-x-hidden relative z-10">
        <div className="flex-1 w-full overflow-y-auto">
          {children}
        </div>
      </main>
    </div>
  );
};