const { Pool } = require('pg');
const { AzureOpenAI } = require('openai');

const pool = new Pool({
  host: 'database-1-instance-1.c96kcq8k0crv.ap-southeast-1.rds.amazonaws.com',
  port: 5432,
  user: 'postgres',
  password: 'postgres',
  database: 'postgres'
});

const embeddingsClient = new AzureOpenAI({
  endpoint: 'https://zhupocai01.openai.azure.com/',
  apiKey: '********************************',
  apiVersion: '2023-05-15',
});

async function generateEmbedding(text) {
  try {
    const truncatedText = text.slice(0, 8000);
    const response = await embeddingsClient.embeddings.create({
      model: 'text-embedding-3-small',
      input: truncatedText,
    });
    return response.data[0]?.embedding || null;
  } catch (error) {
    console.error('Failed to generate embedding:', error);
    return null;
  }
}

async function debugVectorSearch() {
  try {
    const userQuery = "15 under police probe for sharing Singpass credentials";
    console.log(`🔍 Debugging vector search for: "${userQuery}"`);
    
    const queryEmbedding = await generateEmbedding(userQuery);
    if (!queryEmbedding) {
      console.log('❌ Failed to generate embedding');
      return;
    }
    
    console.log(`✅ Generated embedding with ${queryEmbedding.length} dimensions`);
    
    // Test the exact search query used by the application
    const searchQuery = `
      SELECT 
        url, title, published_date, source, main_text, threat_actor, victim, 
        incident_type, vulnerabilities, tags,
        (vector <=> $1::vector) as distance,
        (1 - (vector <=> $1::vector)) as similarity
      FROM public.articles 
      WHERE vector IS NOT NULL
      ORDER BY vector <=> $1::vector
      LIMIT 10;
    `;
    
    const vectorParam = `[${queryEmbedding.join(',')}]`;
    const result = await pool.query(searchQuery, [vectorParam]);
    
    console.log(`📊 Vector search returned ${result.rows.length} articles:`);
    
    let foundSingpass = false;
    result.rows.forEach((row, index) => {
      const isSingpass = row.title.toLowerCase().includes('singpass');
      const marker = isSingpass ? '🎯' : '  ';
      if (isSingpass) foundSingpass = true;
      
      console.log(`${marker}${index + 1}. ${row.title.substring(0, 60)}... (similarity: ${row.similarity.toFixed(4)})`);
    });
    
    if (!foundSingpass) {
      console.log('\n❌ Singpass article NOT in top 10 results');
      
      // Try to find where it ranks
      const rankQuery = `
        SELECT 
          title,
          (1 - (vector <=> $1::vector)) as similarity,
          ROW_NUMBER() OVER (ORDER BY vector <=> $1::vector) as rank
        FROM public.articles 
        WHERE vector IS NOT NULL
        AND title ILIKE '%singpass%'
        ORDER BY vector <=> $1::vector;
      `;
      
      const rankResult = await pool.query(rankQuery, [vectorParam]);
      
      if (rankResult.rows.length > 0) {
        const singpassRow = rankResult.rows[0];
        console.log(`🎯 Singpass article rank: ${singpassRow.rank} with similarity ${singpassRow.similarity.toFixed(4)}`);
        
        if (singpassRow.similarity < 0.1) {
          console.log('💡 Solution: Need to search more articles (increase LIMIT) or lower threshold even more');
        }
      }
    } else {
      console.log('\n✅ Singpass article found in results!');
    }
    
    // Check if there's an issue with the vector similarity calculation
    console.log('\n🧪 Testing simple vector existence for Singpass article...');
    const vectorCheckQuery = `
      SELECT 
        title,
        vector IS NOT NULL as has_vector,
        LENGTH(vector::text) as vector_length
      FROM public.articles 
      WHERE title ILIKE '%singpass%'
    `;
    
    const vectorCheckResult = await pool.query(vectorCheckQuery);
    vectorCheckResult.rows.forEach(row => {
      console.log(`Article: ${row.title}`);
      console.log(`Has Vector: ${row.has_vector}`);
      console.log(`Vector Length: ${row.vector_length}`);
    });
    
  } catch (error) {
    console.error('❌ Error debugging vector search:', error);
  } finally {
    await pool.end();
  }
}

debugVectorSearch();