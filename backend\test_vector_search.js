const { Pool } = require('pg');
const { AzureOpenAI } = require('openai');

const pool = new Pool({
  host: 'database-1-instance-1.c96kcq8k0crv.ap-southeast-1.rds.amazonaws.com',
  port: 5432,
  user: 'postgres',
  password: 'postgres',
  database: 'postgres'
});

// Initialize Azure OpenAI Embeddings client like the main app
const embeddingsClient = new AzureOpenAI({
  endpoint: 'https://zhupocai01.openai.azure.com/',
  apiKey: '********************************',
  apiVersion: '2023-05-15',
});

async function generateEmbedding(text) {
  try {
    const truncatedText = text.slice(0, 8000);
    const response = await embeddingsClient.embeddings.create({
      model: 'text-embedding-3-small',
      input: truncatedText,
    });
    return response.data[0]?.embedding || null;
  } catch (error) {
    console.error('Failed to generate embedding:', error);
    return null;
  }
}

async function testVectorSimilaritySearch() {
  try {
    console.log('Testing vector similarity search for "credential sharing"...');
    
    // Generate embedding for the query
    const queryEmbedding = await generateEmbedding('credential sharing');
    if (!queryEmbedding) {
      console.log('❌ Failed to generate embedding for query');
      return;
    }
    
    console.log(`✅ Generated embedding with ${queryEmbedding.length} dimensions`);
    
    // Test vector similarity search with different thresholds
    const thresholds = [0.1, 0.3, 0.5, 0.7];
    
    for (const threshold of thresholds) {
      console.log(`\n--- Testing with threshold ${threshold} ---`);
      
      const searchQuery = `
        SELECT 
          title, source, published_date,
          (vector <=> $1::vector) as distance,
          (1 - (vector <=> $1::vector)) as similarity
        FROM articles 
        WHERE vector IS NOT NULL
        ORDER BY vector <=> $1::vector
        LIMIT 10;
      `;
      
      const vectorParam = `[${queryEmbedding.join(',')}]`;
      const result = await pool.query(searchQuery, [vectorParam]);
      
      const filtered = result.rows.filter(row => row.similarity >= threshold);
      
      console.log(`Found ${filtered.length} articles above threshold ${threshold}:`);
      filtered.forEach((row, index) => {
        console.log(`${index + 1}. ${row.title} (similarity: ${row.similarity.toFixed(3)})`);
      });
      
      if (filtered.length === 0) {
        console.log('Top 3 closest matches (below threshold):');
        result.rows.slice(0, 3).forEach((row, index) => {
          console.log(`${index + 1}. ${row.title} (similarity: ${row.similarity.toFixed(3)})`);
        });
      }
    }
    
    // Test with "Singpass" specifically
    console.log('\n--- Testing with "Singpass" query ---');
    const singpassEmbedding = await generateEmbedding('Singpass');
    
    const singpassQuery = `
      SELECT 
        title, source, published_date,
        (1 - (vector <=> $1::vector)) as similarity
      FROM articles 
      WHERE vector IS NOT NULL
      ORDER BY vector <=> $1::vector
      LIMIT 5;
    `;
    
    const singpassVectorParam = `[${singpassEmbedding.join(',')}]`;
    const singpassResult = await pool.query(singpassQuery, [singpassVectorParam]);
    
    console.log('Top 5 matches for "Singpass":');
    singpassResult.rows.forEach((row, index) => {
      console.log(`${index + 1}. ${row.title} (similarity: ${row.similarity.toFixed(3)})`);
    });
    
  } catch (error) {
    console.error('Error testing vector similarity search:', error);
  } finally {
    await pool.end();
  }
}

testVectorSimilaritySearch();