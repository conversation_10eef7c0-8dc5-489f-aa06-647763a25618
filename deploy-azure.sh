#!/bin/bash
# Azure Web App deployment script for MIS2 Crawler
# This script ensures proper Playwright browser installation for Azure Web Apps

echo "Starting Azure Web App deployment for MIS2 Crawler..."

# Set environment variables for Azure Web App
export NODE_ENV=production
export WEBSITE_SITE_NAME=${WEBSITE_SITE_NAME:-"mis2-crawler"}

# Navigate to the project directory (usually /home/<USER>/wwwroot)
cd /home/<USER>/wwwroot

echo "Installing Node.js dependencies..."
npm install

echo "Building the application..."
npm run build

echo "Installing Playwright browsers for Azure Web Apps..."
# Try to install with dependencies first
if npx playwright install --with-deps chromium; then
    echo "✅ Playwright browsers installed successfully with dependencies"
else
    echo "⚠️  Failed to install with dependencies, trying without..."
    # Fallback to installing without system dependencies
    if npx playwright install chromium; then
        echo "✅ Playwright browsers installed successfully"
    else
        echo "❌ Failed to install Playwright browsers"
        echo "Will use bundled browser in the application"
    fi
fi

# Set up proper permissions for browser execution
chmod +x /home/<USER>/ms-playwright/chromium-*/chrome-linux/chrome 2>/dev/null || true

echo "Checking Playwright installation..."
npx playwright --version

echo "Creating runtime directories..."
mkdir -p /tmp/playwright-cache
mkdir -p /tmp/screenshots

echo "Setting up environment for Azure Web App..."
export PLAYWRIGHT_BROWSERS_PATH=/home/<USER>/ms-playwright
export PLAYWRIGHT_CACHE_DIR=/tmp/playwright-cache

echo "✅ Azure Web App deployment completed successfully!"
echo "Application should be ready to handle crawler requests."

# Start the application
echo "Starting the application..."
npm start 