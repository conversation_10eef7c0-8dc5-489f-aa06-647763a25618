const { Pool } = require('pg');

const pool = new Pool({
  host: 'database-1-instance-1.c96kcq8k0crv.ap-southeast-1.rds.amazonaws.com',
  port: 5432,
  user: 'postgres',
  password: 'postgres',
  database: 'postgres'
});

async function checkVectors() {
  try {
    // First, check table structure
    const columnsResult = await pool.query(`
      SELECT column_name, data_type 
      FROM information_schema.columns 
      WHERE table_name = 'articles'
      ORDER BY ordinal_position
    `);
    
    console.log('Articles table columns:');
    columnsResult.rows.forEach(row => {
      console.log(`- ${row.column_name}: ${row.data_type}`);
    });
    
    // Check total articles
    const totalResult = await pool.query('SELECT COUNT(*) as total FROM articles');
    console.log(`\nTotal articles: ${totalResult.rows[0].total}`);
    
    // Check articles with vectors
    const vectorResult = await pool.query('SELECT COUNT(*) as with_vectors FROM articles WHERE vector IS NOT NULL');
    console.log(`Articles with vectors: ${vectorResult.rows[0].with_vectors}`);
    
    // Check articles without vectors
    const noVectorResult = await pool.query('SELECT COUNT(*) as without_vectors FROM articles WHERE vector IS NULL');
    console.log(`Articles without vectors: ${noVectorResult.rows[0].without_vectors}`);
    
    // Sample of articles (using proper column name)
    const sampleResult = await pool.query(`
      SELECT url, title, vector IS NOT NULL as has_vector
      FROM articles 
      LIMIT 5
    `);
    
    console.log('\nSample articles:');
    sampleResult.rows.forEach(row => {
      console.log(`URL: ${row.url.substring(0,30)}..., Title: ${row.title.substring(0,50)}..., Has Vector: ${row.has_vector}`);
    });
    
    // Check for credential-related articles
    const credentialResult = await pool.query(`
      SELECT url, title, vector IS NOT NULL as has_vector
      FROM articles 
      WHERE title ILIKE '%credential%' OR title ILIKE '%singpass%' OR title ILIKE '%sharing%'
      LIMIT 5
    `);
    
    console.log('\nCredential/Singpass/Sharing related articles:');
    credentialResult.rows.forEach(row => {
      console.log(`Title: ${row.title}, Has Vector: ${row.has_vector}`);
    });
    
  } catch (error) {
    console.error('Error checking vectors:', error);
  } finally {
    await pool.end();
  }
}

checkVectors();