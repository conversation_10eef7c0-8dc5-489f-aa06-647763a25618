#!/usr/bin/env node

// Simple test script to verify Playwright browser installation
const { chromium } = require('playwright');

async function testBrowser() {
  console.log('🧪 Testing Playwright browser installation...');
  
  // Log environment information
  console.log('Environment variables:');
  console.log('- NODE_ENV:', process.env.NODE_ENV);
  console.log('- WEBSITE_SITE_NAME:', process.env.WEBSITE_SITE_NAME);
  console.log('- PLAYWRIGHT_BROWSERS_PATH:', process.env.PLAYWRIGHT_BROWSERS_PATH);
  console.log('- PLAYWRIGHT_CACHE_DIR:', process.env.PLAYWRIGHT_CACHE_DIR);
  
  // Check if browser files exist
  const fs = require('fs');
  const path = require('path');
  
  const browserPaths = [
    '/home/<USER>/ms-playwright',
    '/tmp/playwright-cache',
    process.env.PLAYWRIGHT_BROWSERS_PATH
  ].filter(<PERSON><PERSON>an);
  
  console.log('\nChecking browser file system:');
  for (const browserPath of browserPaths) {
    if (fs.existsSync(browserPath)) {
      console.log(`✅ Path exists: ${browserPath}`);
      try {
        const contents = fs.readdirSync(browserPath);
        console.log(`   Contents: ${contents.join(', ')}`);
      } catch (err) {
        console.log(`   ❌ Could not read contents: ${err.message}`);
      }
    } else {
      console.log(`❌ Path does not exist: ${browserPath}`);
    }
  }
  
  // Test browser launch
  console.log('\nTesting browser launch...');
  
  const isAzureWebApp = process.env.WEBSITE_SITE_NAME && process.env.NODE_ENV === 'production';
  
  const launchOptions = {
    headless: true,
    args: [
      '--no-sandbox',
      '--disable-setuid-sandbox',
      '--disable-dev-shm-usage',
      '--disable-gpu',
      '--disable-background-timer-throttling',
      '--disable-backgrounding-occluded-windows',
      '--disable-renderer-backgrounding',
      '--disable-features=TranslateUI',
      '--disable-ipc-flooding-protection',
      '--disable-extensions',
      '--disable-default-apps',
      '--single-process'
    ]
  };
  
  if (isAzureWebApp) {
    console.log('Adding Azure Web App specific arguments...');
    launchOptions.args.push(
      '--disable-web-security',
      '--disable-features=VizDisplayCompositor',
      '--disable-background-networking',
      '--disable-client-side-phishing-detection',
      '--disable-component-update',
      '--disable-domain-reliability',
      '--disable-features=AudioServiceOutOfProcess',
      '--disable-hang-monitor',
      '--disable-popup-blocking',
      '--disable-print-preview',
      '--disable-prompt-on-repost',
      '--disable-speech-api',
      '--disable-sync',
      '--hide-scrollbars',
      '--ignore-gpu-blacklist',
      '--metrics-recording-only',
      '--mute-audio',
      '--no-default-browser-check',
      '--no-first-run',
      '--no-pings',
      '--no-zygote',
      '--password-store=basic',
      '--use-gl=swiftshader',
      '--use-mock-keychain'
    );
  }
  
  console.log('Launch options:', JSON.stringify(launchOptions, null, 2));
  
  try {
    const browser = await chromium.launch(launchOptions);
    console.log('✅ Browser launched successfully!');
    
    const page = await browser.newPage();
    console.log('✅ Page created successfully!');
    
    await page.goto('data:text/html,<html><body><h1>Test Page</h1></body></html>');
    console.log('✅ Page navigation successful!');
    
    const title = await page.title();
    console.log(`✅ Page title: ${title}`);
    
    await browser.close();
    console.log('✅ Browser closed successfully!');
    
    console.log('\n🎉 All tests passed! Browser installation is working correctly.');
    
  } catch (error) {
    console.error('\n❌ Browser test failed:');
    console.error('Error:', error.message);
    console.error('Stack:', error.stack);
    
    if (error.message.includes('executable')) {
      console.error('\n💡 Troubleshooting suggestions:');
      console.error('1. Run: npx playwright install chromium');
      console.error('2. Check if Chrome is installed: which google-chrome');
      console.error('3. Verify permissions: ls -la /home/<USER>/ms-playwright/');
      console.error('4. Check Azure Web App logs for deployment issues');
    }
    
    process.exit(1);
  }
}

// Run the test
testBrowser().catch(console.error); 