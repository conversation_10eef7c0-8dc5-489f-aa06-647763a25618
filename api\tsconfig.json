{"extends": "../tsconfig.json", "compilerOptions": {"target": "es2018", "module": "commonjs", "lib": ["es2018"], "outDir": "../dist/api", "rootDir": ".", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "resolveJsonModule": true, "allowSyntheticDefaultImports": true}, "include": ["**/*.ts"], "exclude": ["node_modules", "dist"]}