@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom animations for modern tab transitions */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Utility classes for animations */
.animate-fadeIn {
  animation: fadeIn 0.5s ease-out forwards;
}

.animate-slideInRight {
  animation: slideInRight 0.4s ease-out forwards;
}

.animate-scaleIn {
  animation: scaleIn 0.3s ease-out forwards;
}

/* Enhanced hover effects */
.hover\:scale-102:hover {
  transform: scale(1.02);
}

/* Smooth transitions for all interactive elements */
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
}

/* Enhanced shadows */
.shadow-glow {
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
}

.shadow-glow-purple {
  box-shadow: 0 0 20px rgba(147, 51, 234, 0.3);
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: #000;
  color: #fff;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* Custom gradient backgrounds */
.bg-gradient-cyber {
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f172a 100%);
}

.bg-gradient-card {
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.8) 0%, rgba(30, 41, 59, 0.6) 100%);
  backdrop-filter: blur(16px);
}

/* Enhanced button styles */
.btn-primary {
  @apply bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold py-2 px-4 rounded-lg transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105;
}

.btn-secondary {
  @apply bg-gray-700/50 hover:bg-gray-600/50 text-gray-300 hover:text-white font-medium py-2 px-4 rounded-lg transition-all duration-300 border border-white/10 hover:border-white/20;
}

/* Tab indicator animation */
.tab-indicator {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Line clamping utilities for text truncation */
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

/* Additional animations for the orbital timeline */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes ping {
  75%, 100% {
    transform: scale(2);
    opacity: 0;
  }
}

/* Custom animations for the orbital timeline */
.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-ping {
  animation: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite;
}

/* Enhanced transitions */
.duration-300 {
  transition-duration: 300ms;
}

.duration-700 {
  transition-duration: 700ms;
}

.duration-1000 {
  transition-duration: 1000ms;
}

/* Backdrop blur */
.backdrop-blur-lg {
  backdrop-filter: blur(16px);
}

.backdrop-blur-md {
  backdrop-filter: blur(12px);
}

/* Custom gradients */
.bg-gradient-orbital {
  background: linear-gradient(to bottom right, #6366f1, #3b82f6, #14b8a6);
}

/* Enhanced z-index utilities */
.z-10 {
  z-index: 10;
}

.z-50 {
  z-index: 50;
}

/* Custom shadows */
.shadow-orbital {
  box-shadow: 0 0 15px rgba(255, 255, 255, 0.1);
}

/* Custom transforms */
.scale-125 {
  transform: scale(1.25);
}

.scale-150 {
  transform: scale(1.5);
}

/* Enhanced opacity values */
.opacity-70 {
  opacity: 0.7;
}

.opacity-50 {
  opacity: 0.5;
}

/* Custom border opacity */
.border-white\/10 {
  border-color: rgba(255, 255, 255, 0.1);
}

.border-white\/20 {
  border-color: rgba(255, 255, 255, 0.2);
}

.border-white\/30 {
  border-color: rgba(255, 255, 255, 0.3);
}

.border-white\/40 {
  border-color: rgba(255, 255, 255, 0.4);
}

/* Custom background opacity */
.bg-black\/90 {
  background-color: rgba(0, 0, 0, 0.9);
}

.bg-white\/50 {
  background-color: rgba(255, 255, 255, 0.5);
}

.bg-white\/80 {
  background-color: rgba(255, 255, 255, 0.8);
}

/* Custom text opacity */
.text-white\/70 {
  color: rgba(255, 255, 255, 0.7);
}

.text-white\/80 {
  color: rgba(255, 255, 255, 0.8);
}

/* Plasma Animated Background (updated) */
.wrapper {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  filter: blur(254px);
  z-index: 0;
  pointer-events: none;
}

.gradient {
  position: absolute;
  border-radius: 100%;
  opacity: 0.6;
  mix-blend-mode: screen;
  animation-iteration-count: infinite;
  animation-timing-function: cubic-bezier(0.1, 0, 0.9, 1);
}

.gradient-1 {
  background: rgb(29, 27, 27);
  width: 700px;
  height: 700px;
  animation-duration: 11s;
  opacity: 0.75;
  left: 60%;
  top: 40%;
  z-index: -2;
  animation-name: animation-gradient-1;
}

.gradient-2 {
  background: rgb(166, 0, 255);
  width: 600px;
  height: 600px;
  animation-duration: 6s;
  opacity: 0.32;
  left: 40%;
  top: 60%;
  z-index: -1;
  animation-name: animation-gradient-2;
}

.gradient-3 {
  background: rgb(46, 46, 132);
  width: 500px;
  height: 500px;
  animation-duration: 10s;
  opacity: 0.65;
  left: 50%;
  top: 50%;
  z-index: -3;
  animation-name: animation-gradient-3;
}

@keyframes animation-gradient-1 {
  0% {
    transform: translateY(-50%) translateX(-50%) rotate(-20deg) translateX(20%);
  }
  25% {
    transform: translateY(-50%) translateX(-50%) skew(-15deg, -15deg)
      rotate(80deg) translateX(30%);
  }
  50% {
    transform: translateY(-50%) translateX(-50%) rotate(180deg) translateX(25%);
  }
  75% {
    transform: translateY(-50%) translateX(-50%) skew(15deg, 15deg)
      rotate(240deg) translateX(15%);
  }
  100% {
    transform: translateY(-50%) translateX(-50%) rotate(340deg) translateX(20%);
  }
}

@keyframes animation-gradient-2 {
  0% {
    transform: translateY(-50%) translateX(-50%) rotate(40deg) translateX(-20%);
  }
  25% {
    transform: translateY(-50%) translateX(-50%) skew(15deg, 15deg)
      rotate(110deg) translateX(-5%);
  }
  50% {
    transform: translateY(-50%) translateX(-50%) rotate(210deg) translateX(-35%);
  }
  75% {
    transform: translateY(-50%) translateX(-50%) skew(-15deg, -15deg)
      rotate(300deg) translateX(-10%);
  }
  100% {
    transform: translateY(-50%) translateX(-50%) rotate(400deg) translateX(-20%);
  }
}

@keyframes animation-gradient-3 {
  0% {
    transform: translateY(-50%) translateX(-50%) translateX(-15%)
      translateY(10%);
  }
  20% {
    transform: translateY(-50%) translateX(-50%) translateX(20%)
      translateY(-30%);
  }
  40% {
    transform: translateY(-50%) translateX(-50%) translateX(-25%)
      translateY(-15%);
  }
  60% {
    transform: translateY(-50%) translateX(-50%) translateX(30%) translateY(20%);
  }
  80% {
    transform: translateY(-50%) translateX(-50%) translateX(5%) translateY(35%);
  }
  100% {
    transform: translateY(-50%) translateX(-50%) translateX(-15%)
      translateY(10%);
  }
}