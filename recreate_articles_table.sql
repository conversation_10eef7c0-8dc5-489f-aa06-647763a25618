-- <PERSON><PERSON>t to Recreate the articles table
-- Make sure you have the vector extension installed first

-- Enable the vector extension (required for vector similarity search)
CREATE EXTENSION IF NOT EXISTS vector;

-- Drop table if it exists (use with caution)
-- DROP TABLE IF EXISTS public.articles;

-- Create the articles table
CREATE TABLE public.articles (
    id SERIAL PRIMARY KEY,
    url TEXT NOT NULL UNIQUE,
    title VARCHAR(500) NOT NULL,
    published_date DATE NOT NULL,
    main_text TEXT NOT NULL,
    victim_country VARCHAR(255) DEFAULT 'Unknown',
    source VARCHAR(255) NOT NULL,
    incident_type JSON DEFAULT '[]'::json,
    vulnerabilities JSON DEFAULT '[]'::json,
    victim VARCHAR(200),
    impact JSON DEFAULT '["medium"]'::json,
    threat_actor VARCHAR(200),
    tags JSON DEFAULT '[]'::json,
    scrape_timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    vector vector(1536) -- Adjust dimension based on your embedding model (1536 for text-embedding-3-small)
);

-- <PERSON>reate indexes for better performance
CREATE INDEX idx_articles_published_date ON public.articles(published_date DESC);
CREATE INDEX idx_articles_source ON public.articles(source);
CREATE INDEX idx_articles_scrape_timestamp ON public.articles(scrape_timestamp DESC);
CREATE INDEX idx_articles_victim_country ON public.articles(victim_country);
CREATE INDEX idx_articles_threat_actor ON public.articles(threat_actor);

-- Create GIN indexes for JSON columns to enable efficient searches
CREATE INDEX idx_articles_incident_type ON public.articles USING gin(incident_type);
CREATE INDEX idx_articles_vulnerabilities ON public.articles USING gin(vulnerabilities);
CREATE INDEX idx_articles_tags ON public.articles USING gin(tags);
CREATE INDEX idx_articles_impact ON public.articles USING gin(impact);

-- Create vector index for similarity search (HNSW is good for cosine distance)
CREATE INDEX idx_articles_vector ON public.articles USING hnsw (vector vector_cosine_ops);

-- Add a text search index for full-text search on title and main_text
CREATE INDEX idx_articles_text_search ON public.articles USING gin(to_tsvector('english', title || ' ' || main_text));

-- Verify the table was created successfully
\d public.articles;

-- Show table info
SELECT 
    column_name, 
    data_type, 
    character_maximum_length, 
    is_nullable, 
    column_default
FROM information_schema.columns 
WHERE table_name = 'articles' AND table_schema = 'public'
ORDER BY ordinal_position; 