// API utility functions for handling URLs in both development and production

export const getApiUrl = (path: string): string => {
  // Check if we're in production
  const isProduction = window.location.hostname !== 'localhost' && 
                      !window.location.hostname.includes('localhost') &&
                      !window.location.hostname.includes('127.0.0.1') &&
                      window.location.protocol === 'https:';
  
  if (isProduction) {
    // In production, use relative URLs (same origin)
    return path;
  } else {
    // In development, use full URL with backend port
    return `http://localhost:3001${path}`;
  }
};

export const apiRequest = async (path: string, options?: RequestInit): Promise<Response> => {
  const url = getApiUrl(path);
  
  const defaultOptions: RequestInit = {
    headers: {
      'Content-Type': 'application/json',
      ...options?.headers,
    },
    ...options,
  };
  
  return fetch(url, defaultOptions);
}; 