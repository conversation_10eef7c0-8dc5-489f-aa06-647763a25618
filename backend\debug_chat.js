const { Pool } = require('pg');
const { AzureOpenAI } = require('openai');

const pool = new Pool({
  host: 'database-1-instance-1.c96kcq8k0crv.ap-southeast-1.rds.amazonaws.com',
  port: 5432,
  user: 'postgres',
  password: 'postgres',
  database: 'postgres'
});

const embeddingsClient = new AzureOpenAI({
  endpoint: 'https://zhupocai01.openai.azure.com/',
  apiKey: '********************************',
  apiVersion: '2023-05-15',
});

async function generateEmbedding(text) {
  try {
    const truncatedText = text.slice(0, 8000);
    const response = await embeddingsClient.embeddings.create({
      model: 'text-embedding-3-small',
      input: truncatedText,
    });
    return response.data[0]?.embedding || null;
  } catch (error) {
    console.error('Failed to generate embedding:', error);
    return null;
  }
}

async function debugChatSearch() {
  try {
    const queryText = "pass Credential Sharing";
    console.log(`🔍 Debugging chat search for: "${queryText}"`);
    
    // Step 1: Generate embedding
    console.log('Step 1: Generating embedding...');
    const queryEmbedding = await generateEmbedding(queryText);
    if (!queryEmbedding) {
      console.log('❌ Failed to generate embedding');
      return;
    }
    console.log(`✅ Generated embedding with ${queryEmbedding.length} dimensions`);
    
    // Step 2: Run the exact same query as the searchSimilarArticlesForChat function
    console.log('Step 2: Running similarity search...');
    
    const searchQuery = `
      SELECT 
        title, published_date, source, url,
        (vector <=> $1::vector) as distance,
        (1 - (vector <=> $1::vector)) as similarity
      FROM public.articles 
      WHERE vector IS NOT NULL
      ORDER BY vector <=> $1::vector
      LIMIT $2;
    `;
    
    const vectorParam = `[${queryEmbedding.join(',')}]`;
    const result = await pool.query(searchQuery, [vectorParam, 3]);
    
    console.log(`📊 Query returned ${result.rows.length} articles total`);
    
    // Step 3: Apply threshold filtering like the function does
    const threshold = 0.3;
    console.log(`🎯 Applying threshold: ${threshold}`);
    
    const filtered = result.rows.filter(row => row.similarity >= threshold);
    console.log(`✨ Articles above threshold ${threshold}: ${filtered.length}`);
    
    // Show all results with their similarity scores
    console.log('\n📈 All results (regardless of threshold):');
    result.rows.forEach((row, index) => {
      const aboveThreshold = row.similarity >= threshold ? '✅' : '❌';
      console.log(`${index + 1}. ${aboveThreshold} ${row.title} (similarity: ${row.similarity.toFixed(4)})`);
    });
    
    // Step 4: Test with even lower threshold
    console.log('\n🧪 Testing with threshold 0.2:');
    const filtered02 = result.rows.filter(row => row.similarity >= 0.2);
    console.log(`Articles above 0.2: ${filtered02.length}`);
    
    console.log('\n🧪 Testing with threshold 0.1:');
    const filtered01 = result.rows.filter(row => row.similarity >= 0.1);
    console.log(`Articles above 0.1: ${filtered01.length}`);
    
    // Step 5: Check if Singpass article is in the results
    console.log('\n🎯 Looking for Singpass article specifically:');
    const singpassFound = result.rows.find(row => 
      row.title.toLowerCase().includes('singpass') || 
      row.title.toLowerCase().includes('credential')
    );
    
    if (singpassFound) {
      console.log(`Found Singpass/credential article: ${singpassFound.title} (similarity: ${singpassFound.similarity.toFixed(4)})`);
    } else {
      console.log('❌ Singpass/credential article not in top results');
      
      // Check if it exists at all in the database with embedding
      const singpassCheck = await pool.query(`
        SELECT title, vector IS NOT NULL as has_vector
        FROM articles 
        WHERE (title ILIKE '%singpass%' OR title ILIKE '%credential%')
        AND vector IS NOT NULL
        LIMIT 5
      `);
      
      console.log(`\n📋 Articles with 'singpass' or 'credential' in database: ${singpassCheck.rows.length}`);
      singpassCheck.rows.forEach(row => {
        console.log(`- ${row.title} (has vector: ${row.has_vector})`);
      });
    }
    
  } catch (error) {
    console.error('❌ Error debugging chat search:', error);
  } finally {
    await pool.end();
  }
}

debugChatSearch();