import React, { useState, useEffect, useRef } from 'react';
import { Shield, Lock, Eye, EyeOff, Key, Globe, Database, Search, Monitor, Cpu, Wifi, Activity, Zap, FileText, Users, Settings, Cloud, AlertTriangle, Building, Newspaper } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import RadialOrbitalTimeline from '@/components/ui/radial-orbital-timeline';

interface LoginPageProps {
  onLogin: () => void;
}

const timelineData = [
  {
    id: 1,
    title: "BleepingComputer",
    date: "ACTIVE",
    content: "Leading cybersecurity news platform covering the latest data breaches, malware campaigns, and security vulnerabilities affecting businesses and consumers worldwide.",
    category: "Cybersecurity News",
    icon: Shield,
    relatedIds: [2, 8],
    status: "completed" as const,
    energy: 95,
  },
  {
    id: 2,
    title: "The Hacker News",
    date: "ACTIVE",
    content: "Premier source for cybersecurity professionals featuring in-depth analysis of emerging threats, APT campaigns, and zero-day exploits from around the globe.",
    category: "Cybersecurity News",
    icon: Zap,
    relatedIds: [1, 3],
    status: "in-progress" as const,
    energy: 88,
  },
  {
    id: 3,
    title: "CSA Singapore",
    date: "ACTIVE",
    content: "Singapore's national cybersecurity agency providing critical alerts, threat advisories, and regulatory guidance to protect the nation's digital infrastructure.",
    category: "Government Advisory",
    icon: AlertTriangle,
    relatedIds: [2, 4],
    status: "completed" as const,
    energy: 92,
  },
  {
    id: 4,
    title: "CISA Advisories",
    date: "ACTIVE",
    content: "U.S. Cybersecurity and Infrastructure Security Agency delivering authoritative threat intelligence, vulnerability assessments, and incident response guidance.",
    category: "Government Advisory",
    icon: Shield,
    relatedIds: [3, 5],
    status: "in-progress" as const,
    energy: 82,
  },
  {
    id: 5,
    title: "Microsoft Security",
    date: "ACTIVE",
    content: "Microsoft's elite threat intelligence team tracking nation-state actors, sophisticated malware families, and enterprise-targeted cyberattacks across the digital landscape.",
    category: "Vendor Intelligence",
    icon: Building,
    relatedIds: [4, 6],
    status: "completed" as const,
    energy: 90,
  },
  {
    id: 6,
    title: "Palo Alto Unit 42",
    date: "ACTIVE",
    content: "World-renowned threat research division specializing in advanced persistent threats, cloud security vulnerabilities, and next-generation malware analysis.",
    category: "Vendor Intelligence",
    icon: Shield,
    relatedIds: [5, 7],
    status: "in-progress" as const,
    energy: 78,
  },
  {
    id: 7,
    title: "Talos Intelligence",
    date: "ACTIVE",
    content: "Cisco's premier security research organization delivering comprehensive threat intelligence on global cybercriminal operations and cutting-edge attack vectors.",
    category: "Vendor Intelligence",
    icon: Database,
    relatedIds: [6, 8],
    status: "completed" as const,
    energy: 85,
  },
  {
    id: 8,
    title: "The Straits Times",
    date: "ACTIVE",
    content: "Singapore's leading newspaper providing timely coverage of regional cyber incidents, digital transformation impacts, and technology policy developments.",
    category: "General News",
    icon: Newspaper,
    relatedIds: [7, 1],
    status: "completed" as const,
    energy: 98,
  },
];

export default function LoginPage({ onLogin }: LoginPageProps) {
  const [accessCode, setAccessCode] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [pulseCenter, setPulseCenter] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    const interval = setInterval(() => {
      setPulseCenter(prev => !prev);
    }, 3000);
    return () => clearInterval(interval);
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!accessCode) {
      setError('Access code is required');
      return;
    }

    setIsLoading(true);
    setError('');

    // Simulate authentication
    setTimeout(() => {
      if (accessCode === '12345678') {
        onLogin();
      } else {
        setError('Invalid access code');
        setAccessCode('');
      }
      setIsLoading(false);
    }, 1500);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSubmit(e as any);
    }
  };

  return (
    <div className="relative w-full h-screen overflow-hidden">
      {/* Radial Orbital Timeline Background */}
      <div className="absolute inset-0 z-0">
        <RadialOrbitalTimeline timelineData={timelineData} />
      </div>

      {/* Central Login Interface */}
      <div className="absolute inset-0 flex items-center justify-center z-20 pointer-events-none">
        <div className="relative">
          {/* Enhanced Central Core */}
          <div className={`absolute inset-0 w-96 h-96 rounded-full transition-all duration-1000 pointer-events-none ${
            pulseCenter ? 'bg-gradient-to-br from-indigo-500/20 via-purple-500/20 to-pink-500/20' : 'bg-gradient-to-br from-indigo-500/10 via-purple-500/10 to-pink-500/10'
          } animate-pulse`}>
            <div className="absolute inset-4 rounded-full border border-white/20 animate-ping opacity-30"></div>
            <div className="absolute inset-8 rounded-full border border-white/15 animate-ping opacity-20" style={{ animationDelay: '0.5s' }}></div>
            <div className="absolute inset-12 rounded-full border border-white/10 animate-ping opacity-10" style={{ animationDelay: '1s' }}></div>
          </div>

          {/* Main Login Container */}
          <div className="relative w-96 h-96 flex flex-col items-center justify-center bg-black/90 rounded-full border border-white/30 backdrop-blur-lg shadow-2xl pointer-events-auto">
            {/* Logo Container */}
            <div className="mb-0.3 relative">
              <div className="w-32 h-32 rounded-full bg-black flex items-center justify-center overflow-hidden">
                <img 
                  src="/mis.png" 
                  alt="MIS Logo" 
                  className="w-28 h-28 object-contain"
                  onError={(e) => {
                    // Fallback to icon if image doesn't load
                    (e.target as HTMLImageElement).style.display = 'none';
                    (e.target as HTMLImageElement).nextElementSibling?.classList.remove('hidden');
                  }}
                />
                <Key className="hidden w-14 h-14 text-white" />
              </div>
            </div>

            {/* Title */}
            <h1 className="text-2xl font-bold text-white mb-2 tracking-wider">
              SECURE ACCESS
            </h1>
            <p className="text-sm text-white/70 mb-6 text-center px-4">
              Enter authorization code to proceed
            </p>

            {/* Access Code Input */}
            <form onSubmit={handleSubmit} className="w-full px-16">
              <div className="relative mb-4">
                <div className="relative">
                  <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-white/50" />
                  <input
                    ref={inputRef}
                    type={showPassword ? 'text' : 'password'}
                    value={accessCode}
                    onChange={(e) => setAccessCode(e.target.value)}
                    onKeyPress={handleKeyPress}
                    placeholder="Access Code"
                    className="w-full bg-white/10 border border-white/20 rounded-lg py-2.5 pl-10 pr-12 text-white placeholder-white/50 focus:outline-none focus:border-white/40 focus:bg-white/15 transition-all duration-300 text-sm"
                    maxLength={8}
                    disabled={isLoading}
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-white/50 hover:text-white/80 transition-colors"
                    disabled={isLoading}
                  >
                    {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                  </button>
                </div>
                {error && (
                  <p className="text-red-400 text-xs mt-2 text-center animate-fadeIn">
                    {error}
                  </p>
                )}
              </div>

              {/* Submit Button */}
              <Button
                type="submit"
                disabled={isLoading || !accessCode}
                className="relative h-10 px-4 overflow-hidden bg-zinc-900 transition-all duration-200 group w-full shadow-none"
              >
                {/* Gradient background effect */}
                <div className="absolute inset-0 bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 opacity-40 group-hover:opacity-80 blur transition-opacity duration-500" />
                
                {/* Content */}
                <div className="relative flex items-center justify-center">
                  {isLoading ? (
                    <div className="flex items-center justify-center">
                      <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin mr-2"></div>
                      <span className="text-white">Authenticating...</span>
                    </div>
                  ) : (
                    <span className="text-white">Access System</span>
                  )}
                </div>
              </Button>
            </form>

            {/* Status Indicator */}
            <div className="mt-4 flex items-center text-xs text-white/50">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse mr-2"></div>
              System Online
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Info */}
    </div>
  );
}