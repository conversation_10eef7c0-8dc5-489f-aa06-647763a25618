const { Pool } = require('pg');

const pool = new Pool({
  host: 'database-1-instance-1.c96kcq8k0crv.ap-southeast-1.rds.amazonaws.com',
  port: 5432,
  user: 'postgres',
  password: 'postgres',
  database: 'postgres'
});

async function verifySingpassArticle() {
  try {
    console.log('🔍 Verifying Singpass article exists in database...');
    
    // Check if Singpass article exists
    const existsResult = await pool.query(`
      SELECT title, source, published_date, vector IS NOT NULL as has_vector, url
      FROM articles 
      WHERE title ILIKE '%singpass%'
    `);
    
    console.log(`📊 Found ${existsResult.rows.length} articles with 'singpass' in title:`);
    existsResult.rows.forEach(row => {
      console.log(`- ${row.title}`);
      console.log(`  Source: ${row.source}`);
      console.log(`  URL: ${row.url}`);
      console.log(`  Has Vector: ${row.has_vector}`);
      console.log(`  Published: ${row.published_date}`);
      console.log('---');
    });
    
    if (existsResult.rows.length === 0) {
      console.log('❌ No Singpass articles found!');
      
      // Check for similar terms
      const similarResult = await pool.query(`
        SELECT title, source
        FROM articles 
        WHERE title ILIKE '%credential%' OR title ILIKE '%sharing%' OR title ILIKE '%police%'
        LIMIT 5
      `);
      
      console.log(`\n🔍 Articles with credential/sharing/police terms:`);
      similarResult.rows.forEach(row => {
        console.log(`- ${row.title} (${row.source})`);
      });
    }
    
    // Check total articles with vectors
    const totalVectorResult = await pool.query(`
      SELECT COUNT(*) as count 
      FROM articles 
      WHERE vector IS NOT NULL
    `);
    
    console.log(`\n📊 Total articles with vectors: ${totalVectorResult.rows[0].count}`);
    
    // Check if there are other schemas
    const schemaResult = await pool.query(`
      SELECT schemaname, tablename 
      FROM pg_tables 
      WHERE tablename = 'articles'
    `);
    
    console.log(`\n📋 Tables named 'articles' in database:`);
    schemaResult.rows.forEach(row => {
      console.log(`- ${row.schemaname}.${row.tablename}`);
    });
    
  } catch (error) {
    console.error('❌ Error verifying Singpass article:', error);
  } finally {
    await pool.end();
  }
}

verifySingpassArticle();