{"name": "magic-patterns-vite-template", "version": "0.0.1", "private": true, "type": "module", "main": "backend/dist/server.js", "scripts": {"dev": "npx vite", "build": "npx vite build", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "preview": "npx vite preview", "backend:install": "cd backend && npm install", "backend:dev": "cd backend && npm run dev", "backend:build": "cd backend && npm run build", "setup": "npm install && npm run backend:install", "dev:full": "concurrently \"npm run backend:dev\" \"npm run dev\"", "install:browsers": "cd backend && npx playwright install", "start": "cd backend && npm start", "fullstack:deploy": "npm install && npm run build && cd backend && npm install && npm run build && npm start", "deploy": "npm run fullstack:deploy"}, "dependencies": {"@amcharts/amcharts5": "^5.13.3", "@amcharts/amcharts5-geodata": "^5.1.4", "@radix-ui/react-slot": "^1.2.3", "@xyflow/react": "^12.8.1", "axios": "^1.6.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "d3-force": "^3.0.0", "d3-hierarchy": "^3.1.2", "lucide-react": "^0.441.0", "markmap-lib": "^0.18.12", "markmap-toolbar": "^0.18.12", "markmap-view": "^0.18.12", "motion": "^12.23.0", "react": "^18.3.1", "react-dom": "^18.3.1", "recharts": "^3.1.0", "socket.io-client": "^4.7.4", "tailwind-merge": "^3.3.1", "uuid": "^9.0.1", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "latest", "postcss": "latest", "tailwindcss": "3.4.17", "typescript": "^5.5.4", "vite": "^5.2.0"}, "devDependencies": {"@types/node": "^20.11.18", "@types/react": "^18.3.1", "@types/react-dom": "^18.3.1", "@types/uuid": "^9.0.8", "@typescript-eslint/eslint-plugin": "^5.54.0", "@typescript-eslint/parser": "^5.54.0", "concurrently": "^8.2.2", "eslint": "^8.50.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.1"}}