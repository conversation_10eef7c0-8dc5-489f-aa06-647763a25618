const { Pool } = require('pg');
const { AzureOpenAI } = require('openai');

const pool = new Pool({
  host: 'database-1-instance-1.c96kcq8k0crv.ap-southeast-1.rds.amazonaws.com',
  port: 5432,
  user: 'postgres',
  password: 'postgres',
  database: 'postgres'
});

const embeddingsClient = new AzureOpenAI({
  endpoint: 'https://zhupocai01.openai.azure.com/',
  apiKey: '********************************',
  apiVersion: '2023-05-15',
});

async function generateEmbedding(text) {
  try {
    const truncatedText = text.slice(0, 8000);
    const response = await embeddingsClient.embeddings.create({
      model: 'text-embedding-3-small',
      input: truncatedText,
    });
    return response.data[0]?.embedding || null;
  } catch (error) {
    console.error('Failed to generate embedding:', error);
    return null;
  }
}

async function testExactQuery() {
  try {
    const userQuery = "15 under police probe for sharing Singpass credentials";
    console.log(`🔍 Testing exact user query: "${userQuery}"`);
    
    const queryEmbedding = await generateEmbedding(userQuery);
    if (!queryEmbedding) {
      console.log('❌ Failed to generate embedding');
      return;
    }
    
    // Search specifically for the Singpass article to see its similarity score
    const singpassQuery = `
      SELECT 
        title,
        (1 - (vector <=> $1::vector)) as similarity
      FROM articles 
      WHERE vector IS NOT NULL
      AND title ILIKE '%singpass%'
      ORDER BY vector <=> $1::vector;
    `;
    
    const vectorParam = `[${queryEmbedding.join(',')}]`;
    const singpassResult = await pool.query(singpassQuery, [vectorParam]);
    
    console.log(`\n🎯 Singpass article similarity:`);
    singpassResult.rows.forEach(row => {
      console.log(`"${row.title}" = ${row.similarity.toFixed(4)} similarity`);
    });
    
    // Now get the top 10 overall matches to see where Singpass ranks
    const allQuery = `
      SELECT 
        title,
        (1 - (vector <=> $1::vector)) as similarity,
        ROW_NUMBER() OVER (ORDER BY vector <=> $1::vector) as rank
      FROM articles 
      WHERE vector IS NOT NULL
      ORDER BY vector <=> $1::vector
      LIMIT 10;
    `;
    
    const allResult = await pool.query(allQuery, [vectorParam]);
    
    console.log(`\n📊 Top 10 matches for user query:`);
    allResult.rows.forEach(row => {
      const isSingpass = row.title.toLowerCase().includes('singpass');
      const marker = isSingpass ? '🎯' : '  ';
      console.log(`${marker}${row.rank}. ${row.title} (${row.similarity.toFixed(4)})`);
    });
    
    // Check what threshold would be needed
    const singpassSimilarity = singpassResult.rows[0]?.similarity || 0;
    console.log(`\n💡 To find the Singpass article, threshold needs to be: ${singpassSimilarity.toFixed(4)} or lower`);
    
    if (singpassSimilarity < 0.1) {
      console.log('❌ Even threshold 0.1 won\'t find it - the query terms don\'t match well');
    } else if (singpassSimilarity < 0.2) {
      console.log('⚠️ Need threshold below 0.2 to find it');
    } else {
      console.log('✅ Current threshold should find it');
    }
    
    // Test a better query
    console.log('\n🧪 Testing improved query...');
    const betterQuery = "Singpass credential sharing police investigation scam";
    const betterEmbedding = await generateEmbedding(betterQuery);
    const betterParam = `[${betterEmbedding.join(',')}]`;
    const betterSingpassResult = await pool.query(singpassQuery, [betterParam]);
    
    console.log(`Better query "${betterQuery}" similarity:`);
    betterSingpassResult.rows.forEach(row => {
      console.log(`"${row.title}" = ${row.similarity.toFixed(4)} similarity`);
    });
    
  } catch (error) {
    console.error('❌ Error testing exact query:', error);
  } finally {
    await pool.end();
  }
}

testExactQuery();