import React from 'react';
import { HomeIcon, SearchIcon, BarChartIcon, SettingsIcon, MessageCircleIcon } from 'lucide-react';

interface SidebarProps {
  currentView?: 'dashboard' | 'crawler' | 'study' | 'chat' | 'scheduler';
  onViewChange?: (view: 'dashboard' | 'crawler' | 'study' | 'chat' | 'scheduler') => void;
}

export const Sidebar = ({ 
  currentView = 'dashboard', 
  onViewChange
}: SidebarProps) => {
  const isProduction = window.location.hostname !== 'localhost' && 
                      !window.location.hostname.includes('localhost');
  
  const isRealTimeCrawlerUnavailable = false; // Enable all features in both dev and production

  const menuItems = [{
    icon: <HomeIcon size={20} />,
    label: 'Dashboard',
    active: currentView === 'dashboard',
    onClick: () => onViewChange?.('dashboard')
  }, {
    icon: <SearchIcon size={20} />,
    label: 'Crawler',
    active: currentView === 'crawler',
    onClick: () => onViewChange?.('crawler'),
    disabled: isRealTimeCrawlerUnavailable
  }, {
    icon: <BarChartIcon size={20} />,
    label: 'Study',
    active: currentView === 'study',
    onClick: () => onViewChange?.('study')
  }, {
    icon: <MessageCircleIcon size={20} />,
    label: 'Chat',
    active: currentView === 'chat',
    onClick: () => onViewChange?.('chat')
  }, {
    icon: <SettingsIcon size={20} />,
    label: 'Scheduler',
    active: currentView === 'scheduler',
    onClick: () => onViewChange?.('scheduler'),
    disabled: isRealTimeCrawlerUnavailable
  }];


  return (
    <div className="w-64 bg-black border-r border-white/10 flex flex-col h-screen fixed left-0 top-0 z-50">
      {/* Logo/Header */}
      <div className="p-6 border-b border-white/10">
        <div className="flex items-center justify-center">
          <img src="/mis.png" alt="MIS Logo" className="h-16 w-auto" />
        </div>
      </div>

      {/* Navigation Menu */}
      <div className="flex-1 flex flex-col min-h-0">
        <nav className="flex-1 p-6 min-h-0">
          <ul className="space-y-2">
            {menuItems.map((item, index) => (
              <li key={index}>
                <button
                  onClick={item.onClick}
                  disabled={item.disabled}
                  style={{
                    transition: item.active 
                      ? 'background 0.5s ease-out, backdrop-filter 0.5s ease-out, transform 0.5s ease-out, box-shadow 0.5s ease-out, color 0.5s ease-out'
                      : 'background 0.5s ease-out, backdrop-filter 0.5s ease-out, transform 0.5s ease-out, box-shadow 0.5s ease-out, color 0.5s ease-out'
                  }}
                  className={`group w-full flex items-center space-x-3 px-4 py-3 rounded-xl relative overflow-hidden ${
                    item.active
                      ? 'bg-gradient-to-r from-white/20 via-white/10 to-white/5 backdrop-blur-xl border border-white/30 text-white shadow-2xl shadow-white/10 scale-105 translate-x-1'
                      : item.disabled
                      ? 'text-gray-500 cursor-not-allowed opacity-50'
                      : 'text-gray-300 hover:bg-gradient-to-r hover:from-white/8 hover:via-white/4 hover:to-transparent hover:backdrop-blur-lg hover:text-white hover:scale-102 hover:shadow-lg hover:shadow-white/5'
                  }`}
                >
                  {/* Animated background glow effect for active state */}
                  {item.active && (
                    <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 via-purple-500/10 to-pink-500/10 opacity-50 animate-pulse" />
                  )}
                  
                  {/* Shimmer effect overlay */}
                  {item.active && (
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-1000 ease-in-out" />
                  )}
                  
                  <div className={`relative z-10 transition-all duration-300 ${item.active ? 'drop-shadow-lg' : ''}`}>
                    {item.icon}
                  </div>
                  <span className={`relative z-10 font-medium transition-all duration-300 ${item.active ? 'drop-shadow-sm' : ''}`}>
                    {item.label}
                  </span>
                  {item.disabled && (
                    <span className="ml-auto text-xs bg-gray-600 px-2 py-1 rounded">
                      N/A
                    </span>
                  )}
                </button>
              </li>
            ))}
          </ul>
        </nav>
      </div>

      {/* Footer */}
      <div className="p-6 border-t border-white/10">
        <div className="text-center">
          <div className="text-xs text-gray-500">
            {isProduction ? 'Production' : 'Development'} Mode
          </div>
          <div className="text-xs text-gray-600 mt-1">
            v1.0.0
          </div>
        </div>
      </div>
    </div>
  );
};