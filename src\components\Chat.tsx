import React, { useState, useRef, useEffect } from 'react';
import {
  UserIcon,
  LoaderIcon,
  SparklesIcon,
  ExternalLinkIcon,
  CalendarIcon,
  GlobeIcon,
  ShieldIcon,
  LightbulbIcon,
  TrendingUpIcon,
  AlertTriangleIcon,
  Trash2Icon
} from 'lucide-react';
import { ButtonColorful } from '@/components/ui/button-colorful';

interface ChatMessage {
  id: string;
  type: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  sources?: ChatSource[];
  isLoading?: boolean;
  isStreaming?: boolean;
  isFromStorage?: boolean;
}

interface ChatSource {
  id: number;
  title: string;
  source: string;
  published_date: string;
  similarity: string;
  url?: string;
  snippet: string;
}

interface SampleQuestion {
  icon: React.ReactNode;
  text: string;
  category: string;
}

interface FormattedMessageProps {
  content: string;
  isStreaming?: boolean;
  skipAnimation?: boolean;
}

const FormattedMessage: React.FC<FormattedMessageProps> = ({ content, isStreaming, skipAnimation = false }) => {
  const [displayedContent, setDisplayedContent] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [hasBeenStreamed, setHasBeenStreamed] = useState(false);

  useEffect(() => {
    if (isStreaming) {
      // Mark that this content has been streamed
      setHasBeenStreamed(true);
      // Show content immediately when streaming with smooth transition
      setDisplayedContent(content);
      setIsTyping(true); // Keep typing indicator during streaming
      return;
    }

    if (!content) {
      setDisplayedContent('');
      setHasBeenStreamed(false);
      setIsTyping(false);
      return;
    }

    // Skip typing animation if content was already streamed or if skipAnimation is true
    if (hasBeenStreamed || skipAnimation) {
      setDisplayedContent(content);
      setIsTyping(false);
      return;
    }

    // Start typing animation only for non-streamed messages
    setIsTyping(true);
    setDisplayedContent('');

    let index = 0;
    const typingSpeed = 3; // Smoother, faster typing

    const timer = setInterval(() => {
      setDisplayedContent(content.slice(0, index + 1));
      index++;

      if (index >= content.length) {
        clearInterval(timer);
        setIsTyping(false);
      }
    }, typingSpeed);

    return () => clearInterval(timer);
  }, [content, isStreaming, hasBeenStreamed, skipAnimation]);

  const formatContent = (text: string) => {
    if (!text) return '';

    // Clean up the text and format it properly
    let formatted = text;

    // Remove excessive asterisks and format bold text
    formatted = formatted.replace(/\*\*(.*?)\*\*/g, '<strong class="font-semibold text-white">$1</strong>');

    // Format section headers that start with numbers and asterisks
    formatted = formatted.replace(/(\d+)\.\s*\*\*(.*?)\*\*/g, '<div class="mb-4 mt-4"><span class="font-bold text-blue-300 text-lg">$1. $2</span></div>');

    // Format regular numbered lists
    formatted = formatted.replace(/^(\d+)\.\s+(.+)$/gm, '<div class="mb-3 ml-4 flex items-start"><span class="font-semibold text-blue-300 mr-2 min-w-[1.5rem]">$1.</span><span class="flex-1">$2</span></div>');

    // Format bullet points
    formatted = formatted.replace(/^[-•]\s+(.+)$/gm, '<div class="mb-2 ml-6 flex items-start"><span class="text-blue-300 mr-2 mt-1">•</span><span class="flex-1">$1</span></div>');

    // Format section headers (lines that end with colon and aren't part of numbered lists)
    formatted = formatted.replace(/^([^0-9].+):$/gm, '<h4 class="font-bold text-white mt-6 mb-3 text-lg border-b border-gray-600 pb-2">$1</h4>');

    // Format sub-headers (lines in all caps)
    formatted = formatted.replace(/^([A-Z\s]{3,}):$/gm, '<h5 class="font-semibold text-blue-200 mt-4 mb-2">$1</h5>');

    // Clean up multiple newlines
    formatted = formatted.replace(/\n{3,}/g, '\n\n');

    // Convert single newlines to breaks but preserve paragraph spacing
    formatted = formatted.replace(/\n\n/g, '|||PARAGRAPH|||');
    formatted = formatted.replace(/\n/g, '<br/>');
    formatted = formatted.replace(/\|\|\|PARAGRAPH\|\|\|/g, '</p><p class="mt-4">');

    // Wrap everything in a paragraph
    if (formatted && !formatted.startsWith('<')) {
      formatted = '<p>' + formatted + '</p>';
    }

    return formatted;
  };

  const currentContent = displayedContent;
  const formattedContent = formatContent(currentContent);

  return (
    <div className="space-y-2">
      <div
        className="leading-relaxed text-gray-100 transition-all duration-150 ease-out"
        dangerouslySetInnerHTML={{ __html: formattedContent }}
      />
      {(isStreaming || isTyping) && (
        <span className="inline-block w-0.5 h-4 bg-blue-400 animate-pulse ml-1 mt-1 transition-opacity duration-300"></span>
      )}
    </div>
  );
};

const CHAT_STORAGE_KEY = 'cyber-intelligence-chat-history';

// Helper functions for localStorage
const saveChatHistory = (messages: ChatMessage[]) => {
  try {
    // Filter out loading messages and convert dates to strings for storage
    const messagesToSave = messages
      .filter(msg => !msg.isLoading)
      .map(msg => ({
        ...msg,
        timestamp: msg.timestamp.toISOString(),
        isStreaming: false, // Reset streaming state when saving
        isFromStorage: undefined // Don't save the storage flag
      }));
    localStorage.setItem(CHAT_STORAGE_KEY, JSON.stringify(messagesToSave));
  } catch (error) {
    console.warn('Failed to save chat history:', error);
  }
};

const loadChatHistory = (): ChatMessage[] => {
  try {
    const saved = localStorage.getItem(CHAT_STORAGE_KEY);
    if (!saved) return [];

    const parsed = JSON.parse(saved);
    // Convert timestamp strings back to Date objects and mark as from storage
    return parsed.map((msg: any) => ({
      ...msg,
      timestamp: new Date(msg.timestamp),
      isFromStorage: true
    }));
  } catch (error) {
    console.warn('Failed to load chat history:', error);
    return [];
  }
};

export const Chat: React.FC = () => {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [shouldAutoScroll, setShouldAutoScroll] = useState(true);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);

  const sampleQuestions: SampleQuestion[] = [
    {
      icon: <ShieldIcon size={16} className="text-blue-400" />,
      text: "What are the latest ransomware attack trends?",
      category: "Threat Intelligence"
    },
    {
      icon: <AlertTriangleIcon size={16} className="text-red-400" />,
      text: "How do threat actors typically gain initial access?",
      category: "Attack Vectors"
    },
    {
      icon: <TrendingUpIcon size={16} className="text-green-400" />,
      text: "What vulnerabilities are being actively exploited?",
      category: "Vulnerabilities"
    },
    {
      icon: <GlobeIcon size={16} className="text-purple-400" />,
      text: "Which countries are most targeted by cyber attacks?",
      category: "Geopolitics"
    },
    {
      icon: <LightbulbIcon size={16} className="text-yellow-400" />,
      text: "What are effective defense strategies against APT groups?",
      category: "Defense"
    },
    {
      icon: <SparklesIcon size={16} className="text-cyan-400" />,
      text: "How has the threat landscape evolved recently?",
      category: "Trends"
    }
  ];

  const scrollToBottom = () => {
    if (shouldAutoScroll) {
      messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    }
  };

  // Check if user is near bottom of chat
  const checkScrollPosition = () => {
    if (!messagesContainerRef.current) return;

    const { scrollTop, scrollHeight, clientHeight } = messagesContainerRef.current;
    const isNearBottom = scrollHeight - scrollTop - clientHeight < 100;
    setShouldAutoScroll(isNearBottom);
  };

  // Load chat history on component mount
  useEffect(() => {
    const savedMessages = loadChatHistory();
    if (savedMessages.length > 0) {
      setMessages(savedMessages);
    }
  }, []);

  // Save chat history whenever messages change (but not during streaming)
  useEffect(() => {
    if (messages.length > 0) {
      const hasStreamingMessage = messages.some(msg => msg.isStreaming || msg.isLoading);
      if (!hasStreamingMessage) {
        saveChatHistory(messages);
      }
    }
  }, [messages]);

  useEffect(() => {
    // Only auto-scroll for new messages, not during streaming updates
    const lastMessage = messages[messages.length - 1];
    if (lastMessage && !lastMessage.isStreaming) {
      scrollToBottom();
    }
  }, [messages.length]); // Only trigger on new messages, not content updates

  const handleSampleQuestion = (question: string) => {
    setInput(question);
    inputRef.current?.focus();
  };

  const handleClearChat = () => {
    setMessages([]);
    localStorage.removeItem(CHAT_STORAGE_KEY);
    setShouldAutoScroll(true);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!input.trim() || isLoading) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      type: 'user',
      content: input.trim(),
      timestamp: new Date()
    };

    const loadingMessage: ChatMessage = {
      id: (Date.now() + 1).toString(),
      type: 'assistant',
      content: '',
      timestamp: new Date(),
      isLoading: true
    };

    setMessages(prev => [...prev, userMessage, loadingMessage]);
    setInput('');
    setIsLoading(true);
    setShouldAutoScroll(true); // Enable auto-scroll for new conversation

    try {
      console.log('🔄 Starting streaming chat request...');

      // Use fetch with ReadableStream for POST streaming  
      // Chat already uses relative URL - this is correct for production
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'text/event-stream'
        },
        body: JSON.stringify({
          message: userMessage.content,
          stream: true
        })
      });

      if (!response.ok) {
        throw new Error('Failed to get response');
      }

      if (!response.body) {
        throw new Error('No response body');
      }

      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let buffer = '';
      let accumulatedContent = '';
      let sources: ChatSource[] = [];

      while (true) {
        const { value, done } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6));

              if (data.type === 'sources') {
                sources = data.sources;
                console.log('📚 Received sources:', sources.length);

                // Update message with sources immediately
                setMessages(prev => prev.map(msg =>
                  msg.id === loadingMessage.id ? {
                    ...msg,
                    isLoading: false,
                    isStreaming: true,
                    sources: sources
                  } : msg
                ));
              } else if (data.type === 'content') {
                accumulatedContent += data.content;

                // Update message with streaming content immediately
                setMessages(prev => prev.map(msg =>
                  msg.id === loadingMessage.id ? {
                    ...msg,
                    content: accumulatedContent,
                    isLoading: false,
                    isStreaming: true,
                    sources: sources
                  } : msg
                ));
              } else if (data.type === 'done') {
                console.log('✅ Streaming completed');
                setIsLoading(false);
                break;
              } else if (data.type === 'error') {
                throw new Error(data.error);
              }
            } catch (parseError) {
              console.warn('Failed to parse streaming data:', parseError);
            }
          }
        }
      }

      // Final update to ensure completion
      setMessages(prev => prev.map(msg =>
        msg.id === loadingMessage.id ? {
          ...msg,
          content: accumulatedContent,
          isLoading: false,
          isStreaming: false,
          sources: sources
        } : msg
      ));

    } catch (error) {
      console.error('Chat streaming error:', error);

      const errorMessage: ChatMessage = {
        id: loadingMessage.id,
        type: 'assistant',
        content: 'I apologize, but I encountered an error while processing your question. Please try again or check if the service is available.',
        timestamp: new Date(),
        isLoading: false
      };

      setMessages(prev => prev.map(msg =>
        msg.id === loadingMessage.id ? errorMessage : msg
      ));
    } finally {
      setIsLoading(false);
    }
  };

  const formatDate = (date: Date) => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  return (
    <div className="flex flex-col h-full text-white relative">

      {/* Messages Area */}
      <div
        ref={messagesContainerRef}
        className="flex-1 overflow-y-auto px-8 py-8 space-y-8 pb-32"
        onScroll={checkScrollPosition}
      >
        {messages.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-full">
            <div className="w-20 h-20 bg-gradient-to-r from-cyan-500 via-blue-500 to-purple-600 rounded-3xl flex items-center justify-center mb-8 shadow-xl shadow-blue-500/20">
              <img src="/ai.svg" alt="AI" className="w-10 h-10" />
            </div>
            <h2 className="text-2xl font-bold mb-4 bg-gradient-to-r from-white to-gray-200 bg-clip-text text-transparent">
              Welcome to Cyber Intelligence Chat
            </h2>


            {/* Sample Questions */}
            <div className="w-full max-w-5xl">
              <h3 className="text-xl font-semibold mb-6 text-center text-gray-300">Explore these topics:</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {sampleQuestions.map((question, index) => (
                  <button
                    key={index}
                    onClick={() => handleSampleQuestion(question.text)}
                    className="group p-6 bg-gray-800/20 hover:bg-gray-800/40 border border-white/10 hover:border-white/20 rounded-2xl transition-colors duration-300 text-left backdrop-blur-xl"
                  >
                    <div className="flex items-center space-x-3 mb-3">
                      <div className="p-2 bg-gradient-to-r from-gray-700 to-gray-600 rounded-lg transition-colors duration-300">
                        {question.icon}
                      </div>
                      <span className="text-xs font-medium text-gray-400 uppercase tracking-wider">
                        {question.category}
                      </span>
                    </div>
                    <p className="text-sm text-gray-300 group-hover:text-white transition-colors duration-300 leading-relaxed">
                      {question.text}
                    </p>
                  </button>
                ))}
              </div>
            </div>
          </div>
        ) : (
          messages.map((message) => (
            <div key={message.id} className={`flex items-start space-x-4 animate-fadeIn ${message.type === 'user' ? 'flex-row-reverse space-x-reverse' : ''
              }`}>
              {/* Avatar */}
              <div className={`w-10 h-10 rounded-2xl flex items-center justify-center flex-shrink-0 shadow-lg ${message.type === 'user'
                ? 'bg-gradient-to-r from-blue-600 to-cyan-600 shadow-blue-500/25'
                : 'bg-gradient-to-r from-purple-600 to-pink-600 shadow-purple-500/25'
                }`}>
                {message.type === 'user' ? (
                  <UserIcon size={18} className="text-white" />
                ) : (
                  <img src="/ai.svg" alt="AI" className="w-5 h-5" />
                )}
              </div>

              {/* Message Content */}
              <div className={`flex-1 ${message.type === 'user' ? 'text-right' : ''}`}>
                <div className={`inline-block p-5 rounded-3xl max-w-4xl shadow-lg backdrop-blur-xl ${message.type === 'user'
                  ? 'bg-gradient-to-r from-blue-600 to-cyan-600 text-white shadow-blue-500/20'
                  : 'bg-gray-800/30 border border-white/10 shadow-gray-800/50'
                  }`}>
                  {message.isLoading ? (
                    <div className="flex items-center space-x-3">
                      <LoaderIcon size={18} className="animate-spin" />
                      <span className="text-gray-300">Analyzing intelligence database...</span>
                    </div>
                  ) : (
                    <div className="prose prose-invert prose-sm max-w-none transition-all duration-200 ease-out">
                      <FormattedMessage
                        content={message.content}
                        isStreaming={message.isStreaming}
                        skipAnimation={message.isFromStorage}
                      />
                    </div>
                  )}
                </div>

                {/* Sources */}
                {message.sources && message.sources.length > 0 && (
                  <div className="mt-6 space-y-3">
                    <h4 className="text-sm font-semibold text-gray-400 flex items-center">
                      <ExternalLinkIcon size={16} className="mr-2" />
                      Intelligence Sources ({message.sources.length})
                    </h4>
                    <div className="grid gap-3">
                      {message.sources.map((source, index) => (
                        <div key={index} className="bg-gray-800/20 border border-white/5 rounded-2xl p-4 backdrop-blur-xl hover:bg-gray-800/30 transition-colors duration-300">
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <h5 className="font-semibold text-sm mb-2 line-clamp-2 text-gray-100">{source.title}</h5>
                              <div className="flex items-center space-x-6 text-xs text-gray-400 mb-3">
                                <div className="flex items-center space-x-1">
                                  <GlobeIcon size={12} />
                                  <span>{source.source}</span>
                                </div>
                                <div className="flex items-center space-x-1">
                                  <CalendarIcon size={12} />
                                  <span>{new Date(source.published_date).toLocaleDateString()}</span>
                                </div>
                                <div className="flex items-center space-x-1">
                                  <SparklesIcon size={12} />
                                  <span className="text-cyan-400 font-medium">{(parseFloat(source.similarity) * 100).toFixed(1)}% match</span>
                                </div>
                              </div>
                              <p className="text-xs text-gray-300 line-clamp-2 leading-relaxed">{source.snippet}</p>
                            </div>
                            {source.url && (
                              <a
                                href={source.url}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="ml-4 p-2 hover:bg-white/10 rounded-xl transition-colors duration-300 group"
                                title="Open source article"
                              >
                                <ExternalLinkIcon size={16} className="text-gray-400 group-hover:text-white transition-colors" />
                              </a>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Timestamp */}
                <div className={`text-xs text-gray-500 mt-3 ${message.type === 'user' ? 'text-right' : 'text-left'
                  }`}>
                  {formatDate(message.timestamp)}
                </div>
              </div>
            </div>
          ))
        )}
        <div ref={messagesEndRef} />
      </div>

      {/* Floating Input Area - aligned with card grid */}
      <div className="fixed bottom-0 flex justify-center items-end z-50 px-8" style={{ left: '16rem', width: 'calc(100% - 16rem)' }}>
        <form onSubmit={handleSubmit} className="w-full max-w-5xl mb-8">
          <div className="bg-gray-800/20 backdrop-blur-2xl border border-white/10 rounded-2xl p-3 shadow-2xl shadow-black/20">
            <div className="flex items-center space-x-3">
              <div className="flex-1 relative">
                <input
                  ref={inputRef}
                  type="text"
                  value={input}
                  onChange={(e) => setInput(e.target.value)}
                  placeholder="Type your message..."
                  disabled={isLoading}
                  className="w-full px-4 py-3 bg-gray-700/30 border border-white/10 rounded-xl focus:outline-none focus:ring-2 focus:ring-cyan-500/50 focus:border-cyan-500/30 text-white placeholder-gray-400 disabled:opacity-50 backdrop-blur-xl transition-all duration-300"
                />
              </div>

              {/* Clear Chat Button */}
              {messages.length > 0 && !isLoading && (
                <button
                  onClick={handleClearChat}
                  className="p-3 bg-gray-700/30 hover:bg-red-600/20 border border-white/10 hover:border-red-500/30 rounded-xl transition-all duration-300 group"
                  title="Clear chat history"
                >
                  <Trash2Icon size={18} className="text-gray-400 group-hover:text-red-400 transition-colors duration-300" />
                </button>
              )}

              {isLoading ? (
                <div className="flex items-center space-x-2 px-4 py-3 bg-gray-700/50 rounded-xl">
                  <LoaderIcon size={20} className="animate-spin text-white" />
                  <span className="hidden sm:block text-white font-medium">Sending</span>
                </div>
              ) : (
                <ButtonColorful
                  type="submit"
                  disabled={!input.trim()}
                  label="Send"
                  className="px-4 py-3 rounded-xl font-semibold shadow-lg disabled:opacity-50 disabled:cursor-not-allowed hover:scale-105 transition-all duration-300"
                />
              )}
            </div>
          </div>
        </form>
      </div>
    </div>
  );
}; 