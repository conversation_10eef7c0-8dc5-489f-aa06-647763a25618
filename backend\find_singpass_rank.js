const { Pool } = require('pg');
const { AzureOpenAI } = require('openai');

const pool = new Pool({
  host: 'database-1-instance-1.c96kcq8k0crv.ap-southeast-1.rds.amazonaws.com',
  port: 5432,
  user: 'postgres',
  password: 'postgres',
  database: 'postgres'
});

const embeddingsClient = new AzureOpenAI({
  endpoint: 'https://zhupocai01.openai.azure.com/',
  apiKey: '********************************',
  apiVersion: '2023-05-15',
});

async function generateEmbedding(text) {
  try {
    const truncatedText = text.slice(0, 8000);
    const response = await embeddingsClient.embeddings.create({
      model: 'text-embedding-3-small',
      input: truncatedText,
    });
    return response.data[0]?.embedding || null;
  } catch (error) {
    console.error('Failed to generate embedding:', error);
    return null;
  }
}

async function findSingpassRank() {
  try {
    const queryText = "pass Credential Sharing";
    console.log(`🔍 Finding Singpass article rank for: "${queryText}"`);
    
    const queryEmbedding = await generateEmbedding(queryText);
    if (!queryEmbedding) {
      console.log('❌ Failed to generate embedding');
      return;
    }
    
    // Search ALL articles to find where Singpass ranks
    const searchQuery = `
      SELECT 
        title, 
        (1 - (vector <=> $1::vector)) as similarity,
        ROW_NUMBER() OVER (ORDER BY vector <=> $1::vector) as rank
      FROM public.articles 
      WHERE vector IS NOT NULL
      ORDER BY vector <=> $1::vector;
    `;
    
    const vectorParam = `[${queryEmbedding.join(',')}]`;
    const result = await pool.query(searchQuery, [vectorParam]);
    
    console.log(`📊 Total articles with vectors: ${result.rows.length}`);
    
    // Find the Singpass article
    const singpassArticle = result.rows.find(row => 
      row.title.toLowerCase().includes('singpass') && 
      row.title.toLowerCase().includes('credential')
    );
    
    if (singpassArticle) {
      console.log(`🎯 Found Singpass article at rank ${singpassArticle.rank}:`);
      console.log(`   Title: ${singpassArticle.title}`);
      console.log(`   Similarity: ${singpassArticle.similarity.toFixed(4)}`);
      
      // Show top 10 results for comparison
      console.log('\n📈 Top 10 most similar articles:');
      result.rows.slice(0, 10).forEach((row, index) => {
        const isSingpass = row.title.toLowerCase().includes('singpass');
        const marker = isSingpass ? '🎯' : '  ';
        console.log(`${marker}${index + 1}. ${row.title} (${row.similarity.toFixed(4)})`);
      });
      
    } else {
      console.log('❌ Singpass article not found in results');
    }
    
    // Test different queries
    const queries = [
      'Singpass credential sharing',
      'Singpass scam',
      'credential sharing scam',
      'digital identity sharing'
    ];
    
    for (const query of queries) {
      console.log(`\n🧪 Testing query: "${query}"`);
      const testEmbedding = await generateEmbedding(query);
      
      const testQuery = `
        SELECT 
          title,
          (1 - (vector <=> $1::vector)) as similarity
        FROM public.articles 
        WHERE vector IS NOT NULL
        AND (title ILIKE '%singpass%' OR title ILIKE '%credential%')
        ORDER BY vector <=> $1::vector
        LIMIT 1;
      `;
      
      const testParam = `[${testEmbedding.join(',')}]`;
      const testResult = await pool.query(testQuery, [testParam]);
      
      if (testResult.rows.length > 0) {
        console.log(`   Best match: ${testResult.rows[0].title} (${testResult.rows[0].similarity.toFixed(4)})`);
      } else {
        console.log('   No matches found');
      }
    }
    
  } catch (error) {
    console.error('❌ Error finding Singpass rank:', error);
  } finally {
    await pool.end();
  }
}

findSingpassRank();