const { Pool } = require('pg');

const pool = new Pool({
  host: 'database-1-instance-1.c96kcq8k0crv.ap-southeast-1.rds.amazonaws.com',
  port: 5432,
  user: 'postgres',
  password: 'postgres',
  database: 'postgres'
});

async function checkVectorExtension() {
  try {
    console.log('🔍 Checking PostgreSQL vector extension...');
    
    // Check if vector extension is installed
    const extensionResult = await pool.query(`
      SELECT extname, extversion 
      FROM pg_extension 
      WHERE extname = 'vector'
    `);
    
    console.log(`📊 Vector extension status: ${extensionResult.rows.length > 0 ? 'INSTALLED' : 'NOT INSTALLED'}`);
    
    if (extensionResult.rows.length > 0) {
      console.log(`Version: ${extensionResult.rows[0].extversion}`);
    } else {
      console.log('❌ Vector extension is not installed!');
      
      // Try to install it
      try {
        console.log('🔧 Attempting to install vector extension...');
        await pool.query('CREATE EXTENSION IF NOT EXISTS vector');
        console.log('✅ Vector extension installed');
      } catch (installError) {
        console.log('❌ Failed to install vector extension:', installError.message);
      }
    }
    
    // Check the actual vector column type
    const columnResult = await pool.query(`
      SELECT column_name, data_type, udt_name
      FROM information_schema.columns 
      WHERE table_name = 'articles' AND column_name = 'vector'
    `);
    
    console.log('\n📋 Vector column info:');
    columnResult.rows.forEach(row => {
      console.log(`Column: ${row.column_name}`);
      console.log(`Data Type: ${row.data_type}`);
      console.log(`UDT Name: ${row.udt_name}`);
    });
    
    // Try a simple vector operation
    console.log('\n🧪 Testing vector operations...');
    
    try {
      const testQuery = `
        SELECT title, vector::text as vector_sample
        FROM articles 
        WHERE vector IS NOT NULL 
        LIMIT 1
      `;
      
      const testResult = await pool.query(testQuery);
      
      if (testResult.rows.length > 0) {
        const vectorText = testResult.rows[0].vector_sample;
        console.log(`Sample vector text: ${vectorText.substring(0, 100)}...`);
        
        // Try to cast it to vector type
        try {
          const castQuery = `SELECT $1::vector as test_vector`;
          await pool.query(castQuery, [vectorText]);
          console.log('✅ Vector casting works');
        } catch (castError) {
          console.log('❌ Vector casting failed:', castError.message);
        }
        
        // Try the cosine distance operator
        try {
          const distanceQuery = `
            SELECT title, (vector <=> $1::vector) as distance
            FROM articles 
            WHERE vector IS NOT NULL 
            LIMIT 1
          `;
          
          const distanceResult = await pool.query(distanceQuery, [vectorText]);
          console.log('✅ Vector distance operator works');
          console.log(`Distance: ${distanceResult.rows[0].distance}`);
        } catch (distanceError) {
          console.log('❌ Vector distance operator failed:', distanceError.message);
        }
      }
      
    } catch (testError) {
      console.log('❌ Vector test failed:', testError.message);
    }
    
  } catch (error) {
    console.error('❌ Error checking vector extension:', error);
  } finally {
    await pool.end();
  }
}

checkVectorExtension();