const { Pool } = require('pg');
const { AzureOpenAI } = require('openai');

const pool = new Pool({
  host: 'database-1-instance-1.c96kcq8k0crv.ap-southeast-1.rds.amazonaws.com',
  port: 5432,
  user: 'postgres',
  password: 'postgres',
  database: 'postgres'
});

const embeddingsClient = new AzureOpenAI({
  endpoint: 'https://zhupocai01.openai.azure.com/',
  apiKey: '********************************',
  apiVersion: '2023-05-15',
});

async function generateEmbedding(text) {
  try {
    const truncatedText = text.slice(0, 8000);
    const response = await embeddingsClient.embeddings.create({
      model: 'text-embedding-3-small',
      input: truncatedText,
    });
    return response.data[0]?.embedding || null;
  } catch (error) {
    console.error('Failed to generate embedding:', error);
    return null;
  }
}

async function debugSearchQuery() {
  try {
    const queryText = "pass Credential Sharing";
    console.log(`🔍 Debugging search query issues for: "${queryText}"`);
    
    const queryEmbedding = await generateEmbedding(queryText);
    if (!queryEmbedding) {
      console.log('❌ Failed to generate embedding');
      return;
    }
    
    console.log(`✅ Generated embedding with ${queryEmbedding.length} dimensions`);
    
    // Test different table references
    const queries = [
      {
        name: 'Without schema prefix',
        sql: `
          SELECT COUNT(*) as count
          FROM articles 
          WHERE vector IS NOT NULL
        `
      },
      {
        name: 'With public schema prefix',
        sql: `
          SELECT COUNT(*) as count
          FROM public.articles 
          WHERE vector IS NOT NULL
        `
      },
      {
        name: 'Similarity search without schema',
        sql: `
          SELECT 
            title,
            (1 - (vector <=> $1::vector)) as similarity
          FROM articles 
          WHERE vector IS NOT NULL
          ORDER BY vector <=> $1::vector
          LIMIT 10
        `
      },
      {
        name: 'Similarity search with schema',
        sql: `
          SELECT 
            title,
            (1 - (vector <=> $1::vector)) as similarity
          FROM public.articles 
          WHERE vector IS NOT NULL
          ORDER BY vector <=> $1::vector
          LIMIT 10
        `
      }
    ];
    
    const vectorParam = `[${queryEmbedding.join(',')}]`;
    
    for (const query of queries) {
      try {
        console.log(`\n🧪 Testing: ${query.name}`);
        
        if (query.sql.includes('$1')) {
          const result = await pool.query(query.sql, [vectorParam]);
          
          if (query.name.includes('COUNT')) {
            console.log(`   Result: ${result.rows[0].count} articles`);
          } else {
            console.log(`   Result: ${result.rows.length} articles returned`);
            result.rows.slice(0, 3).forEach((row, index) => {
              console.log(`   ${index + 1}. ${row.title.substring(0, 50)}... (${row.similarity.toFixed(4)})`);
            });
          }
        } else {
          const result = await pool.query(query.sql);
          console.log(`   Result: ${result.rows[0].count} articles`);
        }
        
      } catch (error) {
        console.log(`   ❌ Error: ${error.message}`);
      }
    }
    
    // Test the exact query from the searchSimilarArticlesForChat function
    console.log('\n🎯 Testing exact searchSimilarArticlesForChat query:');
    
    const exactQuery = `
      SELECT 
        id, title, published_date, source, main_text, threat_actor, victim, 
        incident_type, vulnerabilities, tags, url,
        (vector <=> $1::vector) as distance,
        (1 - (vector <=> $1::vector)) as similarity
      FROM public.articles 
      WHERE vector IS NOT NULL
      ORDER BY vector <=> $1::vector
      LIMIT $2;
    `;
    
    try {
      const exactResult = await pool.query(exactQuery, [vectorParam, 3]);
      console.log(`   ✅ Query executed successfully: ${exactResult.rows.length} articles returned`);
      
      exactResult.rows.forEach((row, index) => {
        console.log(`   ${index + 1}. ${row.title.substring(0, 50)}... (similarity: ${row.similarity.toFixed(4)})`);
      });
      
      // Check if any are above threshold 0.3
      const aboveThreshold = exactResult.rows.filter(row => row.similarity >= 0.3);
      console.log(`   📊 Articles above threshold 0.3: ${aboveThreshold.length}`);
      
    } catch (error) {
      console.log(`   ❌ Exact query error: ${error.message}`);
    }
    
  } catch (error) {
    console.error('❌ Error debugging search query:', error);
  } finally {
    await pool.end();
  }
}

debugSearchQuery();