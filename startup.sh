#!/bin/bash
# Startup script for Azure Web App

echo "🚀 Starting MIS2 Crawler Backend on Azure Web App..."

# Check if we're in Azure Web App environment
if [[ -n "$WEBSITE_SITE_NAME" ]]; then
    echo "✅ Detected Azure Web App environment: $WEBSITE_SITE_NAME"
    
    # Set Azure Web App specific environment variables
    export NODE_ENV=production
    export PLAYWRIGHT_BROWSERS_PATH=/home/<USER>/ms-playwright
    export PLAYWRIGHT_CACHE_DIR=/tmp/playwright-cache
    
    # Create necessary directories
    mkdir -p /tmp/playwright-cache
    mkdir -p /tmp/screenshots
    
    # Check if Playwright browsers are available
    if [[ -d "/home/<USER>/ms-playwright" ]]; then
        echo "✅ Playwright browsers directory found"
        
        # Set executable permissions
        chmod +x /home/<USER>/ms-playwright/chromium-*/chrome-linux/chrome 2>/dev/null || echo "⚠️  Could not set chrome executable permissions"
        
        # List available browsers
        ls -la /home/<USER>/ms-playwright/ || echo "⚠️  Could not list browser directory"
    else
        echo "❌ Playwright browsers not found, attempting to install..."
        
        # Try to install browsers
        if npx playwright install chromium; then
            echo "✅ Playwright browsers installed successfully"
        else
            echo "❌ Failed to install Playwright browsers"
            echo "The application will attempt to use bundled browser"
        fi
    fi
else
    echo "🔧 Running in local development mode"
    export NODE_ENV=development
fi

# Navigate to the backend directory
cd /home/<USER>/wwwroot/backend

# Check if the application is built
if [[ ! -f "dist/server.js" ]]; then
    echo "🔨 Building the application..."
    npm run build
fi

# Start the application
echo "🚀 Starting the Node.js application..."
node dist/server.js 