const { Pool } = require('pg');

const pool = new Pool({
  host: 'database-1-instance-1.c96kcq8k0crv.ap-southeast-1.rds.amazonaws.com',
  port: 5432,
  user: 'postgres',
  password: 'postgres',
  database: 'postgres'
});

async function checkVectorIssue() {
  try {
    console.log('🔍 Investigating vector count discrepancy...');
    
    // Check total articles
    const totalResult = await pool.query('SELECT COUNT(*) as total FROM articles');
    console.log(`📊 Total articles: ${totalResult.rows[0].total}`);
    
    // Check articles with non-null vectors
    const nonNullResult = await pool.query('SELECT COUNT(*) as count FROM articles WHERE vector IS NOT NULL');
    console.log(`📊 Articles with non-NULL vectors: ${nonNullResult.rows[0].count}`);
    
    // Check for empty vectors vs truly NULL vectors
    const emptyVectorResult = await pool.query(`
      SELECT COUNT(*) as count 
      FROM articles 
      WHERE vector IS NOT NULL AND vector::text != 'null' AND vector::text != '' AND vector::text != '[]'
    `);
    console.log(`📊 Articles with valid (non-empty) vectors: ${emptyVectorResult.rows[0].count}`);
    
    // Check what the vector values actually look like
    const sampleVectorResult = await pool.query(`
      SELECT title, 
             vector IS NOT NULL as has_vector,
             CASE 
               WHEN vector IS NULL THEN 'NULL'
               WHEN vector::text = '' THEN 'EMPTY_STRING'
               WHEN vector::text = '[]' THEN 'EMPTY_ARRAY'
               WHEN vector::text = 'null' THEN 'NULL_STRING'
               ELSE 'VALID_VECTOR'
             END as vector_status,
             LENGTH(vector::text) as vector_length
      FROM articles 
      ORDER BY vector IS NOT NULL DESC, title
      LIMIT 20
    `);
    
    console.log('\n📋 Sample of articles and their vector status:');
    sampleVectorResult.rows.forEach(row => {
      console.log(`${row.has_vector ? '✅' : '❌'} ${row.title.substring(0, 50)}... | Status: ${row.vector_status} | Length: ${row.vector_length || 'N/A'}`);
    });
    
    // Check specifically for the Singpass article
    const singpassResult = await pool.query(`
      SELECT title, 
             vector IS NOT NULL as has_vector,
             CASE 
               WHEN vector IS NULL THEN 'NULL'
               WHEN vector::text = '' THEN 'EMPTY_STRING'
               WHEN vector::text = '[]' THEN 'EMPTY_ARRAY'
               WHEN vector::text = 'null' THEN 'NULL_STRING'
               ELSE 'VALID_VECTOR'
             END as vector_status,
             LENGTH(vector::text) as vector_length
      FROM articles 
      WHERE title ILIKE '%singpass%'
    `);
    
    console.log('\n🎯 Singpass article vector status:');
    singpassResult.rows.forEach(row => {
      console.log(`Title: ${row.title}`);
      console.log(`Has Vector: ${row.has_vector}`);
      console.log(`Vector Status: ${row.vector_status}`);
      console.log(`Vector Length: ${row.vector_length || 'N/A'}`);
    });
    
    // Check if there are articles with dimensions that can be counted
    const dimensionCheckResult = await pool.query(`
      SELECT title,
             array_length(string_to_array(trim(both '[]' from vector::text), ','), 1) as dimensions
      FROM articles 
      WHERE vector IS NOT NULL 
        AND vector::text != 'null' 
        AND vector::text != '' 
        AND vector::text != '[]'
        AND vector::text LIKE '[%]'
      LIMIT 5
    `);
    
    console.log('\n🔢 Articles with countable vector dimensions:');
    dimensionCheckResult.rows.forEach(row => {
      console.log(`${row.title.substring(0, 50)}... | Dimensions: ${row.dimensions}`);
    });
    
  } catch (error) {
    console.error('❌ Error checking vector issue:', error);
  } finally {
    await pool.end();
  }
}

checkVectorIssue();