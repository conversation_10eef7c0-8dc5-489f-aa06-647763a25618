<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <system.webServer>
    <webSocket enabled="true" />
    <handlers>
      <add name="iisnode" path="backend/dist/server.js" verb="*" modules="iisnode"/>
    </handlers>
    <rewrite>
      <rules>
        <!-- Debugging rule for Node.js -->
        <rule name="NodeInspector" patternSyntax="ECMAScript" stopProcessing="true">
          <match url="^backend/dist/server.js\/debug[\/]?" />
        </rule>
        
        <!-- API routes go to Node.js server -->
        <rule name="API" stopProcessing="true">
          <match url="^api/.*" />
          <action type="Rewrite" url="backend/dist/server.js"/>
        </rule>
        
        <!-- Socket.io routes go to Node.js server -->
        <rule name="SocketIO" stopProcessing="true">
          <match url="^socket\.io/.*" />
          <action type="Rewrite" url="backend/dist/server.js"/>
        </rule>
        
        <!-- Serve static files from dist folder -->
        <rule name="StaticFiles" stopProcessing="true">
          <match url="^(.*\.(js|css|png|jpg|jpeg|gif|svg|ico|woff|woff2|ttf|eot))$" />
          <conditions>
            <add input="{REQUEST_FILENAME}" matchType="IsFile" />
          </conditions>
          <action type="None" />
        </rule>
        
        <!-- All other routes go to Node.js for React routing -->
        <rule name="ReactApp">
          <match url=".*" />
          <conditions>
            <add input="{REQUEST_FILENAME}" matchType="IsFile" negate="True"/>
            <add input="{REQUEST_FILENAME}" matchType="IsDirectory" negate="True"/>
          </conditions>
          <action type="Rewrite" url="backend/dist/server.js"/>
        </rule>
      </rules>
    </rewrite>
    <security>
      <requestFiltering>
        <hiddenSegments>
          <remove segment="bin"/>
        </hiddenSegments>
      </requestFiltering>
    </security>
    <httpErrors existingResponse="PassThrough" />
    <iisnode watchedFiles="web.config;*.js"/>
  </system.webServer>
</configuration> 